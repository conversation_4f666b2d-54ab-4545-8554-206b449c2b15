-- 修复interactions表缺少is_deleted字段的问题
-- 执行时间: 2025-06-14

USE goose_app;

-- 检查interactions表结构
DESCRIBE interactions;

-- 为interactions表添加缺失字段
ALTER TABLE interactions
ADD COLUMN create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 为新字段添加索引
ALTER TABLE interactions ADD INDEX idx_is_deleted (is_deleted);

-- 更新现有数据，设置默认值
UPDATE interactions SET is_deleted = 0 WHERE is_deleted IS NULL;

-- 验证修复结果
DESCRIBE interactions;

-- 查看数据
SELECT COUNT(*) as total_interactions FROM interactions;
SELECT COUNT(*) as active_interactions FROM interactions WHERE is_deleted = 0;

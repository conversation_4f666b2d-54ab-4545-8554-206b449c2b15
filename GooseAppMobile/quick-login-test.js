#!/usr/bin/env node

/**
 * 快速登录测试
 * 使用特殊验证码快速登录并测试小鹅API
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8080/api/v1';

async function quickLoginAndTest() {
  console.log('🧪 快速登录并测试小鹅功能');
  console.log('================================\n');

  const testPhone = '13800138001'; // 使用不同的手机号避免频率限制
  const testCode = '000000'; // 特殊验证码

  try {
    // 1. 直接登录（使用特殊验证码）
    console.log('1. 使用特殊验证码登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login-by-phone`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: testPhone,
        code: testCode,
        deviceInfo: 'Test Script'
      }),
    });

    const loginResult = await loginResponse.json();
    console.log('登录结果:', {
      status: loginResponse.status,
      data: loginResult
    });

    if (!loginResponse.ok || !loginResult.success) {
      throw new Error(`登录失败: ${loginResult.message}`);
    }

    const { token, ...userInfo } = loginResult.data;
    console.log('✅ 登录成功！');
    console.log(`Token: ${token}`);
    console.log(`用户信息:`, { 
      userId: userInfo.userId, 
      username: userInfo.username, 
      nickname: userInfo.nickname 
    });

    // 2. 测试获取用户小鹅列表
    console.log('\n2. 测试获取用户小鹅列表...');
    const geeseResponse = await fetch(`${BASE_URL}/geese/my`, {
      method: 'GET',
      headers: {
        'satoken': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const geeseResult = await geeseResponse.json();
    console.log('小鹅列表结果:', {
      status: geeseResponse.status,
      data: geeseResult
    });

    if (geeseResponse.ok && geeseResult.success) {
      console.log(`✅ 小鹅数量: ${geeseResult.data.length}`);
      
      if (geeseResult.data.length > 0) {
        const firstGoose = geeseResult.data[0];
        console.log('第一只小鹅:', firstGoose);

        // 3. 测试获取小鹅详情
        console.log('\n3. 测试获取小鹅详情...');
        const detailResponse = await fetch(`${BASE_URL}/geese/${firstGoose.id}`, {
          method: 'GET',
          headers: {
            'satoken': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        const detailResult = await detailResponse.json();
        console.log('小鹅详情结果:', {
          status: detailResponse.status,
          data: detailResult
        });

        if (detailResponse.ok && detailResult.success) {
          console.log('✅ 小鹅详情获取成功');
        }
      } else {
        console.log('⚠️ 用户暂无小鹅，需要先购买小鹅');
      }
    }

    // 4. 测试获取用户统计
    console.log('\n4. 测试获取用户统计...');
    const statsResponse = await fetch(`${BASE_URL}/geese/stats`, {
      method: 'GET',
      headers: {
        'satoken': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const statsResult = await statsResponse.json();
    console.log('用户统计结果:', {
      status: statsResponse.status,
      data: statsResult
    });

    if (statsResponse.ok && statsResult.success) {
      console.log('✅ 用户统计获取成功');
      console.log('统计信息:', statsResult.data);
    }

    console.log('\n✅ 测试完成！前端可以使用这个token进行测试');
    console.log(`测试Token: ${token}`);

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
quickLoginAndTest();

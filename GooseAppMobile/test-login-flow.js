#!/usr/bin/env node

/**
 * 测试登录流程
 * 验证手机号登录API是否正常工作
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8080/api/v1';

async function testLoginFlow() {
  console.log('🧪 测试完整登录流程');
  console.log('================================\n');

  const testPhone = '13800138000';
  const testCode = '000000';

  try {
    // 1. 发送验证码
    console.log('1. 发送验证码...');
    const smsResponse = await fetch(`${BASE_URL}/auth/send-sms-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: testPhone,
        codeType: 1 // 登录验证码
      }),
    });

    const smsResult = await smsResponse.json();
    console.log('发送验证码结果:', {
      status: smsResponse.status,
      data: smsResult
    });

    if (!smsResponse.ok || !smsResult.success) {
      throw new Error(`发送验证码失败: ${smsResult.message}`);
    }

    // 2. 手机号验证码登录
    console.log('\n2. 手机号验证码登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login-by-phone`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: testPhone,
        code: testCode,
        deviceInfo: 'Test Script'
      }),
    });

    const loginResult = await loginResponse.json();
    console.log('登录结果:', {
      status: loginResponse.status,
      data: loginResult
    });

    if (!loginResponse.ok || !loginResult.success) {
      throw new Error(`登录失败: ${loginResult.message}`);
    }

    const { token, ...userInfo } = loginResult.data;

    // 3. 测试获取用户小鹅列表
    console.log('\n3. 测试获取用户小鹅列表...');
    const geeseResponse = await fetch(`${BASE_URL}/geese/my`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const geeseResult = await geeseResponse.json();
    console.log('小鹅列表结果:', {
      status: geeseResponse.status,
      data: geeseResult
    });

    console.log('\n✅ 登录流程测试完成！');
    console.log(`Token: ${token}`);
    console.log(`用户信息:`, { 
      userId: userInfo.userId, 
      username: userInfo.username, 
      nickname: userInfo.nickname 
    });

    if (geeseResult.success && geeseResult.data) {
      console.log(`小鹅数量: ${geeseResult.data.length}`);
      if (geeseResult.data.length > 0) {
        console.log('第一只小鹅:', geeseResult.data[0]);
      }
    }

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testLoginFlow();

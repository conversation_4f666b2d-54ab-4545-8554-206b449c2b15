import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { NewLoginScreen } from '../src/screens/auth/NewLoginScreen';
import { AuthProvider } from '../src/context/AuthContext';

// Mock the navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Mock the auth API
jest.mock('../src/services/authApi', () => ({
  authApi: {
    sendSmsCodeV2: jest.fn(),
  },
}));

// Mock vector icons
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'MaterialCommunityIcons');

const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <NavigationContainer>
        {children}
      </NavigationContainer>
    </AuthProvider>
  </QueryClientProvider>
);

describe('NewLoginScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(
      <TestWrapper>
        <NewLoginScreen />
      </TestWrapper>
    );

    expect(getByText('🦆')).toBeTruthy();
    expect(getByPlaceholderText('手机号')).toBeTruthy();
    expect(getByPlaceholderText('6位验证码')).toBeTruthy();
    expect(getByText('获取验证码')).toBeTruthy();
    expect(getByText('登录 / 注册')).toBeTruthy();
  });

  it('validates phone number input', async () => {
    const { getByPlaceholderText, getByText } = render(
      <TestWrapper>
        <NewLoginScreen />
      </TestWrapper>
    );

    const phoneInput = getByPlaceholderText('手机号');
    const codeButton = getByText('获取验证码');

    // Initially, the code button should be disabled
    await waitFor(() => {
      expect(codeButton.props.accessibilityState?.disabled).toBe(true);
    });

    // Enter invalid phone number
    await act(async () => {
      fireEvent.changeText(phoneInput, '123');
    });

    await waitFor(() => {
      expect(codeButton.props.accessibilityState?.disabled).toBe(true);
    });

    // Enter valid phone number
    await act(async () => {
      fireEvent.changeText(phoneInput, '13812345678');
    });

    await waitFor(() => {
      expect(codeButton.props.accessibilityState?.disabled).toBe(false);
    });
  });

  it('handles verification code input', () => {
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <NewLoginScreen />
      </TestWrapper>
    );

    const codeInput = getByPlaceholderText('6位验证码');
    
    fireEvent.changeText(codeInput, '123456');
    expect(codeInput.props.value).toBe('123456');
  });

  it('shows error message for invalid phone', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(
      <TestWrapper>
        <NewLoginScreen />
      </TestWrapper>
    );

    const phoneInput = getByPlaceholderText('手机号');
    const loginButton = getByText('登录 / 注册');

    await act(async () => {
      fireEvent.changeText(phoneInput, '123');
    });

    await act(async () => {
      fireEvent.press(loginButton);
    });

    await waitFor(() => {
      expect(queryByText('请输入正确的手机号格式')).toBeTruthy();
    });
  });

  it('shows error message for missing verification code', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(
      <TestWrapper>
        <NewLoginScreen />
      </TestWrapper>
    );

    const phoneInput = getByPlaceholderText('手机号');
    const loginButton = getByText('登录 / 注册');

    await act(async () => {
      fireEvent.changeText(phoneInput, '13812345678');
    });

    await act(async () => {
      fireEvent.press(loginButton);
    });

    await waitFor(() => {
      expect(queryByText('请输入6位验证码')).toBeTruthy();
    });
  });

  it('renders bottom navigation correctly', () => {
    const { getByText } = render(
      <TestWrapper>
        <NewLoginScreen />
      </TestWrapper>
    );

    expect(getByText('发现')).toBeTruthy();
    expect(getByText('商城')).toBeTruthy();
    expect(getByText('我的鹅')).toBeTruthy();
    expect(getByText('动态')).toBeTruthy();
    expect(getByText('我的')).toBeTruthy();
  });
});

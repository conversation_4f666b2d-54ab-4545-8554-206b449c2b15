import React, { createContext, useReducer, useEffect, useMemo, useContext, useCallback } from 'react';
import { api } from '../services/api';
import { gooseApi, GooseDetail, InteractionResponse, CooldownInfo } from '../services/gooseApi';
import { ApiResponse } from '../types/api';
import { Goose } from '../types/goose';
import { useAuth } from './AuthContext';

type GooseState = {
  myGeese: Goose[];
  currentGoose: GooseDetail | null;
  loading: boolean;
  error: string | null;
  interactionLoading: boolean;
  interactionError: string | null;
  lastInteractionResult: InteractionResponse | null;
};

type GooseAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_MY_GEESE'; payload: Goose[] }
  | { type: 'SET_CURRENT_GOOSE'; payload: GooseDetail | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_INTERACTION_LOADING'; payload: boolean }
  | { type: 'SET_INTERACTION_ERROR'; payload: string | null }
  | { type: 'SET_INTERACTION_RESULT'; payload: InteractionResponse | null }
  | { type: 'UPDATE_GOOSE_IN_LIST'; payload: Goose }
  | { type: 'CLEAR_INTERACTION_RESULT' };

const gooseReducer = (state: GooseState, action: GooseAction): GooseState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_MY_GEESE':
      return { ...state, myGeese: action.payload, loading: false, error: null };
    case 'SET_CURRENT_GOOSE':
      return { ...state, currentGoose: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_INTERACTION_LOADING':
      return { ...state, interactionLoading: action.payload };
    case 'SET_INTERACTION_ERROR':
      return { ...state, interactionError: action.payload, interactionLoading: false };
    case 'SET_INTERACTION_RESULT':
      return {
        ...state,
        lastInteractionResult: action.payload,
        interactionLoading: false,
        interactionError: null
      };
    case 'UPDATE_GOOSE_IN_LIST':
      return {
        ...state,
        myGeese: state.myGeese.map(goose =>
          goose.id === action.payload.id ? action.payload : goose
        ),
        currentGoose: state.currentGoose?.id === action.payload.id
          ? { ...state.currentGoose, ...action.payload }
          : state.currentGoose
      };
    case 'CLEAR_INTERACTION_RESULT':
      return { ...state, lastInteractionResult: null, interactionError: null };
    default:
      return state;
  }
};

type GooseContextType = {
  state: GooseState;
  fetchMyGeese: () => Promise<void>;
  fetchGooseDetail: (gooseId: number) => Promise<void>;
  setCurrentGoose: (goose: GooseDetail | null) => void;
  feedGoose: (gooseId: number) => Promise<void>;
  waterGoose: (gooseId: number) => Promise<void>;
  cleanGoose: (gooseId: number) => Promise<void>;
  playWithGoose: (gooseId: number) => Promise<void>;
  updateGooseName: (gooseId: number, newName: string) => Promise<void>;
  clearInteractionResult: () => void;
};

export const GooseContext = createContext<GooseContextType | undefined>(undefined);

export const GooseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state: authState } = useAuth();
  const initialState: GooseState = {
    myGeese: [],
    currentGoose: null,
    loading: false,
    error: null,
    interactionLoading: false,
    interactionError: null,
    lastInteractionResult: null,
  };

  const [state, dispatch] = useReducer(gooseReducer, initialState);

  const fetchMyGeese = useCallback(async () => {
    if (!authState.userToken) return;
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const geese = await gooseApi.getMyGeese();
      dispatch({ type: 'SET_MY_GEESE', payload: geese });
    } catch (e: any) {
      const errorMessage = e?.response?.data?.message || e?.message || '获取小鹅列表失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Failed to fetch geese in context:', e);
    }
  }, [authState.userToken]);

  const fetchGooseDetail = useCallback(async (gooseId: number) => {
    if (!authState.userToken) return;
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const gooseDetail = await gooseApi.getGooseDetail(gooseId);
      dispatch({ type: 'SET_CURRENT_GOOSE', payload: gooseDetail });
    } catch (e: any) {
      const errorMessage = e?.response?.data?.message || e?.message || '获取小鹅详情失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Failed to fetch goose detail:', e);
    }
  }, [authState.userToken]);

  useEffect(() => {
    if (authState.userToken) {
      fetchMyGeese();
    }
  }, [authState.userToken, fetchMyGeese]);

  const setCurrentGoose = (goose: GooseDetail | null) => {
    dispatch({ type: 'SET_CURRENT_GOOSE', payload: goose });
  };

  // 互动操作的通用处理函数
  const handleInteraction = useCallback(async (
    gooseId: number,
    interactionFn: () => Promise<InteractionResponse>,
    actionName: string
  ) => {
    dispatch({ type: 'SET_INTERACTION_LOADING', payload: true });
    dispatch({ type: 'CLEAR_INTERACTION_RESULT' });

    try {
      const result = await interactionFn();
      dispatch({ type: 'SET_INTERACTION_RESULT', payload: result });

      // 更新小鹅列表中的数据
      if (result.goose) {
        dispatch({ type: 'UPDATE_GOOSE_IN_LIST', payload: result.goose });
      }

      // 如果当前查看的是这只小鹅，刷新详情
      if (state.currentGoose?.id === gooseId) {
        await fetchGooseDetail(gooseId);
      }

      console.log(`${actionName}成功:`, result);
    } catch (e: any) {
      const errorMessage = e?.response?.data?.message || e?.message || `${actionName}失败`;
      dispatch({ type: 'SET_INTERACTION_ERROR', payload: errorMessage });
      console.error(`${actionName}失败:`, e);
    }
  }, [state.currentGoose?.id, fetchGooseDetail]);

  const feedGoose = useCallback(async (gooseId: number) => {
    await handleInteraction(gooseId, () => gooseApi.feedGoose(gooseId), '喂食');
  }, [handleInteraction]);

  const waterGoose = useCallback(async (gooseId: number) => {
    await handleInteraction(gooseId, () => gooseApi.waterGoose(gooseId), '喂水');
  }, [handleInteraction]);

  const cleanGoose = useCallback(async (gooseId: number) => {
    await handleInteraction(gooseId, () => gooseApi.cleanGoose(gooseId), '清洁');
  }, [handleInteraction]);

  const playWithGoose = useCallback(async (gooseId: number) => {
    await handleInteraction(gooseId, () => gooseApi.playWithGoose(gooseId), '玩耍');
  }, [handleInteraction]);

  const updateGooseName = useCallback(async (gooseId: number, newName: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const updatedGoose = await gooseApi.updateGooseName(gooseId, newName);
      dispatch({ type: 'UPDATE_GOOSE_IN_LIST', payload: updatedGoose });

      // 如果当前查看的是这只小鹅，更新当前小鹅信息
      if (state.currentGoose?.id === gooseId) {
        dispatch({ type: 'SET_CURRENT_GOOSE', payload: { ...state.currentGoose, name: newName } });
      }

      dispatch({ type: 'SET_LOADING', payload: false });
      console.log('更新小鹅名称成功:', updatedGoose);
    } catch (e: any) {
      const errorMessage = e?.response?.data?.message || e?.message || '更新小鹅名称失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('更新小鹅名称失败:', e);
    }
  }, [state.currentGoose]);

  const clearInteractionResult = useCallback(() => {
    dispatch({ type: 'CLEAR_INTERACTION_RESULT' });
  }, []);

  const contextValue = useMemo(() => ({
    state,
    fetchMyGeese,
    fetchGooseDetail,
    setCurrentGoose,
    feedGoose,
    waterGoose,
    cleanGoose,
    playWithGoose,
    updateGooseName,
    clearInteractionResult,
  }), [
    state,
    fetchMyGeese,
    fetchGooseDetail,
    feedGoose,
    waterGoose,
    cleanGoose,
    playWithGoose,
    updateGooseName,
    clearInteractionResult
  ]);

  return (
    <GooseContext.Provider value={contextValue}>
      {children}
    </GooseContext.Provider>
  );
};

// 自定义Hook
export const useGoose = (): GooseContextType => {
  const context = useContext(GooseContext);
  if (!context) {
    throw new Error('useGoose must be used within a GooseProvider');
  }
  return context;
};

export const useGoose = (): GooseContextType => {
  const context = useContext(GooseContext);
  if (!context) {
    throw new Error('useGoose must be used within a GooseProvider');
  }
  return context;
};
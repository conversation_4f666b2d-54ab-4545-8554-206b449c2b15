import { api } from './api';
import { ApiResponse } from '../types/api';
import { Goose } from '../types/goose';

/**
 * 小鹅详情信息接口
 */
export interface GooseDetail extends Goose {
  /** 冷却状态 */
  cooldownStatus?: {
    feed?: CooldownInfo;
    water?: CooldownInfo;
    clean?: CooldownInfo;
    play?: CooldownInfo;
  };
  /** 最近互动记录 */
  recentInteractions?: InteractionRecord[];
  /** 是否需要照顾 */
  needsCare?: boolean;
  /** 下一级所需经验值 */
  expToNextLevel?: number;
}

/**
 * 冷却信息接口
 */
export interface CooldownInfo {
  /** 是否在冷却中 */
  inCooldown: boolean;
  /** 剩余冷却时间(秒) */
  remainingTime: number;
  /** 冷却结束时间 */
  endTime?: string;
}

/**
 * 互动记录接口
 */
export interface InteractionRecord {
  id: number;
  gooseId: number;
  interactionType: string;
  attributeChange: number;
  experienceGained: number;
  createdAt: string;
}

/**
 * 互动操作响应接口
 */
export interface InteractionResponse {
  /** 操作是否成功 */
  success: boolean;
  /** 响应消息 */
  message: string;
  /** 更新后的小鹅信息 */
  goose: Goose;
  /** 获得的经验值 */
  experienceGained?: number;
  /** 属性变化 */
  attributeChange?: number;
  /** 冷却信息 */
  cooldownInfo?: CooldownInfo;
}

/**
 * 用户统计信息接口
 */
export interface UserStats {
  totalGeese: number;
  totalInteractions: number;
  totalExperience: number;
  averageLevel: number;
  needsCareCount: number;
}

/**
 * 小鹅API服务类
 */
class GooseApiService {
  /**
   * 获取用户的小鹅列表
   */
  async getMyGeese(): Promise<Goose[]> {
    try {
      const response = await api.get<Goose[]>('/geese/my');
      return response.data;
    } catch (error) {
      console.error('获取小鹅列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取小鹅详情
   */
  async getGooseDetail(gooseId: number): Promise<GooseDetail> {
    try {
      const response = await api.get<GooseDetail>(`/geese/${gooseId}`);
      return response.data;
    } catch (error) {
      console.error('获取小鹅详情失败:', error);
      throw error;
    }
  }

  /**
   * 更新小鹅名称
   */
  async updateGooseName(gooseId: number, newName: string): Promise<Goose> {
    try {
      const response = await api.put<Goose>(`/geese/${gooseId}/name`, { name: newName });
      return response.data;
    } catch (error) {
      console.error('更新小鹅名称失败:', error);
      throw error;
    }
  }

  /**
   * 喂食操作
   */
  async feedGoose(gooseId: number): Promise<InteractionResponse> {
    try {
      const response = await api.post<InteractionResponse>(`/geese/${gooseId}/feed`);
      return response.data;
    } catch (error) {
      console.error('喂食操作失败:', error);
      throw error;
    }
  }

  /**
   * 喂水操作
   */
  async waterGoose(gooseId: number): Promise<InteractionResponse> {
    try {
      const response = await api.post<InteractionResponse>(`/geese/${gooseId}/water`);
      return response.data;
    } catch (error) {
      console.error('喂水操作失败:', error);
      throw error;
    }
  }

  /**
   * 清洁操作
   */
  async cleanGoose(gooseId: number): Promise<InteractionResponse> {
    try {
      const response = await api.post<InteractionResponse>(`/geese/${gooseId}/clean`);
      return response.data;
    } catch (error) {
      console.error('清洁操作失败:', error);
      throw error;
    }
  }

  /**
   * 玩耍操作
   */
  async playWithGoose(gooseId: number): Promise<InteractionResponse> {
    try {
      const response = await api.post<InteractionResponse>(`/geese/${gooseId}/play`);
      return response.data;
    } catch (error) {
      console.error('玩耍操作失败:', error);
      throw error;
    }
  }

  /**
   * 获取需要照顾的小鹅列表
   */
  async getNeedCareGeese(): Promise<Goose[]> {
    try {
      const response = await api.get<Goose[]>('/geese/need-care');
      return response.data;
    } catch (error) {
      console.error('获取需要照顾的小鹅列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(): Promise<UserStats> {
    try {
      const response = await api.get<UserStats>('/geese/stats');
      return response.data;
    } catch (error) {
      console.error('获取用户统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取互动记录
   */
  async getInteractions(gooseId?: number, page: number = 0, size: number = 10): Promise<InteractionRecord[]> {
    try {
      const endpoint = gooseId 
        ? `/interactions/my?gooseId=${gooseId}&page=${page}&size=${size}`
        : `/interactions/my?page=${page}&size=${size}`;
      const response = await api.get<InteractionRecord[]>(endpoint);
      return response.data;
    } catch (error) {
      console.error('获取互动记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取冷却状态
   */
  async getCooldownStatus(gooseId: number): Promise<CooldownInfo[]> {
    try {
      const response = await api.get<CooldownInfo[]>(`/interactions/cooldown/${gooseId}`);
      return response.data;
    } catch (error) {
      console.error('获取冷却状态失败:', error);
      throw error;
    }
  }

  /**
   * 搜索小鹅
   */
  async searchGeese(keyword: string, page: number = 0, size: number = 10): Promise<Goose[]> {
    try {
      const response = await api.get<Goose[]>(`/geese/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`);
      return response.data;
    } catch (error) {
      console.error('搜索小鹅失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const gooseApi = new GooseApiService();

// 导出便捷方法
export const gooseApiMethods = {
  // 基础查询
  getMyGeese: () => gooseApi.getMyGeese(),
  getGooseDetail: (gooseId: number) => gooseApi.getGooseDetail(gooseId),
  getNeedCareGeese: () => gooseApi.getNeedCareGeese(),
  getUserStats: () => gooseApi.getUserStats(),
  
  // 互动操作
  feedGoose: (gooseId: number) => gooseApi.feedGoose(gooseId),
  waterGoose: (gooseId: number) => gooseApi.waterGoose(gooseId),
  cleanGoose: (gooseId: number) => gooseApi.cleanGoose(gooseId),
  playWithGoose: (gooseId: number) => gooseApi.playWithGoose(gooseId),
  
  // 管理操作
  updateGooseName: (gooseId: number, newName: string) => gooseApi.updateGooseName(gooseId, newName),
  
  // 记录查询
  getInteractions: (gooseId?: number, page?: number, size?: number) => gooseApi.getInteractions(gooseId, page, size),
  getCooldownStatus: (gooseId: number) => gooseApi.getCooldownStatus(gooseId),
  
  // 搜索功能
  searchGeese: (keyword: string, page?: number, size?: number) => gooseApi.searchGeese(keyword, page, size),
};

export default gooseApi;

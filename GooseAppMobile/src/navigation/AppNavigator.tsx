import React from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

// 导入新的页面
import PrototypeLoginScreen from '../screens/auth/PrototypeLoginScreen';
import GooseDetailScreen from '../screens/main/GooseDetailScreen';
import DiscoverScreen from '../screens/main/DiscoverScreen';
import ShopScreen from '../screens/main/ShopScreen';
import SocialFeedScreen from '../screens/main/SocialFeedScreen';
import ProfileScreen from '../screens/main/ProfileScreen';

import { useAuth } from '../context/AuthContext';
import { theme } from '../theme';

// --- 路由参数定义 ---
export type AuthStackParamList = {
  Login: undefined;
};

export type AppStackParamList = {
  Discover: undefined;
  Shop: undefined;
  GooseDetail: undefined;
  SocialFeed: undefined;
  Profile: undefined;
};

// --- 导航器实例 ---
const AuthStack = createStackNavigator<AuthStackParamList>();
const AppStack = createStackNavigator<AppStackParamList>();

// --- 认证流程导航 ---
const AuthScreens = () => (
  <AuthStack.Navigator
    initialRouteName="Login"
    screenOptions={{ headerShown: false }}
  >
    <AuthStack.Screen name="Login" component={PrototypeLoginScreen} />
  </AuthStack.Navigator>
);

// --- 主应用流程 (使用Stack导航，每个页面都有自己的底部导航) ---
const AppScreens = () => (
  <AppStack.Navigator
    initialRouteName="GooseDetail"
    screenOptions={{ headerShown: false }}
  >
    <AppStack.Screen name="Discover" component={DiscoverScreen} />
    <AppStack.Screen name="Shop" component={ShopScreen} />
    <AppStack.Screen name="GooseDetail" component={GooseDetailScreen} />
    <AppStack.Screen name="SocialFeed" component={SocialFeedScreen} />
    <AppStack.Screen name="Profile" component={ProfileScreen} />
  </AppStack.Navigator>
);


// --- 根导航器 ---
export const AppNavigator: React.FC = () => {
  const { state } = useAuth();

  if (state.isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={theme.colors.gooseYellow} />
      </View>
    );
  }

  return (
    <NavigationContainer>
      {state.userToken == null ? <AuthScreens /> : <AppScreens />}
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  placeholderText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  }
});

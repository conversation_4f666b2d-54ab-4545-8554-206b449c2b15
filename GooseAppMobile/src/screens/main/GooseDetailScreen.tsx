import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Alert,
  ScrollView,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { ActivityIndicator, Snackbar } from 'react-native-paper';
import { useGoose } from '../../context/GooseContext';
import { theme } from '../../theme';
import BottomNavigation from '../../components/BottomNavigation';

const { width, height } = Dimensions.get('window');

interface StatCardProps {
  icon: string;
  label: string;
  progress: number;
  color: string;
  gradientColors: string[];
  cooldownInfo?: {
    inCooldown: boolean;
    remainingTime: number;
  };
}

const StatCard: React.FC<StatCardProps> = ({ icon, label, progress, color, gradientColors, cooldownInfo }) => (
  <View style={styles.statCard}>
    <MaterialCommunityIcons name={icon} size={24} color={color} />
    <Text style={styles.statLabel}>{label}</Text>
    <View style={styles.progressBar}>
      <View style={[styles.progressFill, { width: `${progress}%`, backgroundColor: color }]} />
    </View>
    {cooldownInfo?.inCooldown && (
      <Text style={styles.cooldownText}>
        冷却中: {Math.ceil(cooldownInfo.remainingTime / 60)}分钟
      </Text>
    )}
  </View>
);

interface ActionButtonProps {
  icon: string;
  label: string;
  onPress: () => void;
  disabled?: boolean;
  cooldownTime?: number;
}

const ActionButton: React.FC<ActionButtonProps> = ({ icon, label, onPress, disabled, cooldownTime }) => (
  <TouchableOpacity
    style={[styles.actionButton, disabled && styles.actionButtonDisabled]}
    onPress={onPress}
    disabled={disabled}
  >
    <MaterialCommunityIcons
      name={icon}
      size={28}
      color={disabled ? "#9CA3AF" : "#1F2937"}
    />
    <Text style={[styles.actionLabel, disabled && styles.actionLabelDisabled]}>
      {label}
    </Text>
    {cooldownTime && cooldownTime > 0 && (
      <Text style={styles.cooldownText}>
        {Math.ceil(cooldownTime / 60)}分钟
      </Text>
    )}
  </TouchableOpacity>
);

export default function GooseDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { gooseId } = route.params as { gooseId: number };

  const {
    state,
    fetchGooseDetail,
    feedGoose,
    waterGoose,
    cleanGoose,
    playWithGoose,
    clearInteractionResult
  } = useGoose();

  const {
    currentGoose,
    loading,
    error,
    interactionLoading,
    interactionError,
    lastInteractionResult
  } = state;

  const [isLive, setIsLive] = useState(true);
  const [viewers, setViewers] = useState(128);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // 获取小鹅详情
  useFocusEffect(
    useCallback(() => {
      if (gooseId) {
        fetchGooseDetail(gooseId);
      }
    }, [gooseId, fetchGooseDetail])
  );

  // 处理互动结果
  useEffect(() => {
    if (lastInteractionResult) {
      setSnackbarMessage(lastInteractionResult.message);
      setSnackbarVisible(true);
      // 3秒后清除结果
      setTimeout(() => {
        clearInteractionResult();
      }, 3000);
    }
  }, [lastInteractionResult, clearInteractionResult]);

  // 处理互动错误
  useEffect(() => {
    if (interactionError) {
      setSnackbarMessage(interactionError);
      setSnackbarVisible(true);
    }
  }, [interactionError]);

  const handleFeed = async () => {
    if (!currentGoose || interactionLoading) return;
    try {
      await feedGoose(currentGoose.id);
    } catch (error) {
      console.error('喂食失败:', error);
    }
  };

  const handleWater = async () => {
    if (!currentGoose || interactionLoading) return;
    try {
      await waterGoose(currentGoose.id);
    } catch (error) {
      console.error('喂水失败:', error);
    }
  };

  const handleClean = async () => {
    if (!currentGoose || interactionLoading) return;
    try {
      await cleanGoose(currentGoose.id);
    } catch (error) {
      console.error('清洁失败:', error);
    }
  };

  const handlePlay = async () => {
    if (!currentGoose || interactionLoading) return;
    try {
      await playWithGoose(currentGoose.id);
    } catch (error) {
      console.error('玩耍失败:', error);
    }
  };

  const handleGift = () => {
    Alert.alert(
      '转赠功能',
      '转赠功能即将上线，敬请期待！',
      [{ text: '确定', style: 'default' }]
    );
  };

  // 获取冷却信息
  const getCooldownInfo = (type: string) => {
    if (!currentGoose?.cooldownStatus) return undefined;
    const cooldown = currentGoose.cooldownStatus[type as keyof typeof currentGoose.cooldownStatus];
    return cooldown ? {
      inCooldown: cooldown.inCooldown,
      remainingTime: cooldown.remainingTime
    } : undefined;
  };

  if (loading && !currentGoose) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.gooseYellow} />
          <Text style={styles.loadingText}>正在加载小鹅信息...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error && !currentGoose) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchGooseDetail(gooseId)}
          >
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentGoose) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>小鹅信息不存在</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.retryButtonText}>返回</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <MaterialCommunityIcons name="arrow-left" size={20} color="#1F2937" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{currentGoose.name || '未命名的小鹅'}</Text>
          <TouchableOpacity>
            <MaterialCommunityIcons name="dots-horizontal" size={20} color="#1F2937" />
          </TouchableOpacity>
        </View>

        {/* Goose Info */}
        <View style={styles.gooseInfoContainer}>
          <Text style={styles.gooseLevel}>Lv. {currentGoose.level || 1}</Text>
          <Text style={styles.gooseBreed}>
            {currentGoose.gender === 1 ? '♂' : currentGoose.gender === 2 ? '♀' : '?'} {currentGoose.breed || '未知品种'} • {currentGoose.age || 0}周
          </Text>
          <Text style={styles.gooseExperience}>
            经验值: {currentGoose.experience || 0}/{currentGoose.expToNextLevel || 100}
          </Text>
        </View>

        {/* Video Container */}
        <View style={styles.videoContainer}>
          <View style={styles.gooseDisplay}>
            <Text style={styles.gooseEmoji}>🦆</Text>
          </View>
          <View style={styles.videoControls}>
            <View style={styles.liveIndicator}>
              <MaterialCommunityIcons name="video" size={16} color="#fff" />
              <Text style={styles.liveText}>直播中</Text>
            </View>
            <View style={styles.viewerCount}>
              <MaterialCommunityIcons name="eye" size={16} color="#fff" />
              <Text style={styles.viewerText}>{viewers}</Text>
            </View>
          </View>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <StatCard
            icon="heart-pulse"
            label="健康"
            progress={currentGoose.health || 0}
            color="#32CD32"
            gradientColors={['#98FB98', '#32CD32']}
          />
          <StatCard
            icon="food"
            label="饥饿"
            progress={100 - (currentGoose.hunger || 0)} // 饥饿度越低越好，所以反转显示
            color="#FF7F50"
            gradientColors={['#FFA07A', '#FF7F50']}
          />
          <StatCard
            icon="soap"
            label="清洁"
            progress={currentGoose.cleanliness || 0}
            color="#4682B4"
            gradientColors={['#87CEEB', '#4682B4']}
          />
          <StatCard
            icon="emoticon-happy"
            label="快乐"
            progress={currentGoose.happiness || 0}
            color="#FFD700"
            gradientColors={['#FFD700', '#FFA500']}
          />
        </View>

        {/* Actions Grid */}
        <View style={styles.actionsGrid}>
          <ActionButton
            icon="bone"
            label="喂食"
            onPress={handleFeed}
            disabled={interactionLoading || getCooldownInfo('feed')?.inCooldown}
            cooldownTime={getCooldownInfo('feed')?.remainingTime}
          />
          <ActionButton
            icon="water"
            label="喂水"
            onPress={handleWater}
            disabled={interactionLoading || getCooldownInfo('water')?.inCooldown}
            cooldownTime={getCooldownInfo('water')?.remainingTime}
          />
          <ActionButton
            icon="shower"
            label="清洁"
            onPress={handleClean}
            disabled={interactionLoading || getCooldownInfo('clean')?.inCooldown}
            cooldownTime={getCooldownInfo('clean')?.remainingTime}
          />
          <ActionButton
            icon="gamepad-variant"
            label="玩耍"
            onPress={handlePlay}
            disabled={interactionLoading || getCooldownInfo('play')?.inCooldown}
            cooldownTime={getCooldownInfo('play')?.remainingTime}
          />
        </View>

        {/* Gift Button */}
        <TouchableOpacity style={styles.giftButton} onPress={handleGift}>
          <MaterialCommunityIcons name="gift" size={20} color="#1F2937" />
          <Text style={styles.giftButtonText}>转赠给朋友</Text>
        </TouchableOpacity>

        {/* Loading Indicator */}
        {interactionLoading && (
          <View style={styles.interactionLoadingContainer}>
            <ActivityIndicator size="small" color={theme.colors.gooseYellow} />
            <Text style={styles.interactionLoadingText}>处理中...</Text>
          </View>
        )}
      </ScrollView>

      <BottomNavigation />

      {/* Snackbar for feedback */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    background: 'linear-gradient(180deg, #E3F2FD 0%, #FFFDE7 100%)',
    backgroundColor: '#E3F2FD', // Fallback
  },
  content: {
    flex: 1,
    padding: 24,
    paddingBottom: 100, // Space for bottom navigation
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1F2937',
    fontFamily: 'Inter',
  },
  videoContainer: {
    width: '100%',
    height: 250,
    borderRadius: 32,
    backgroundColor: '#333',
    marginBottom: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    position: 'relative',
  },
  gooseDisplay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#87CEEB',
  },
  gooseEmoji: {
    fontSize: 80,
    textAlign: 'center',
  },
  videoControls: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  liveText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  viewerCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewerText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    borderRadius: 32,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: '#1F2937',
    marginTop: 8,
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 10,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 5,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 20,
    paddingVertical: 16,
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1F2937',
  },
  giftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFD700',
    borderRadius: 20,
    paddingVertical: 16,
    gap: 8,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 8,
  },
  giftButtonText: {
    fontSize: 17,
    fontWeight: '700',
    color: '#1F2937',
  },
  // 新增样式
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#1F2937',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: theme.colors.gooseYellow,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  gooseInfoContainer: {
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 20,
    padding: 16,
    marginHorizontal: 8,
  },
  gooseLevel: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  gooseBreed: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 4,
  },
  gooseExperience: {
    fontSize: 14,
    color: '#6B7280',
  },
  actionButtonDisabled: {
    backgroundColor: 'rgba(156, 163, 175, 0.3)',
  },
  actionLabelDisabled: {
    color: '#9CA3AF',
  },
  cooldownText: {
    fontSize: 10,
    color: '#EF4444',
    marginTop: 2,
  },
  interactionLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    marginHorizontal: 8,
    marginTop: 16,
  },
  interactionLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#1F2937',
  },
  snackbar: {
    backgroundColor: theme.colors.gooseYellow,
  },
});

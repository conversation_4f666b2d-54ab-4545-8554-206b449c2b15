import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import BottomNavigation from '../../components/BottomNavigation';

const { width, height } = Dimensions.get('window');

interface StatCardProps {
  icon: string;
  label: string;
  progress: number;
  color: string;
  gradientColors: string[];
}

const StatCard: React.FC<StatCardProps> = ({ icon, label, progress, color, gradientColors }) => (
  <View style={styles.statCard}>
    <MaterialCommunityIcons name={icon} size={24} color={color} />
    <Text style={styles.statLabel}>{label}</Text>
    <View style={styles.progressBar}>
      <View style={[styles.progressFill, { width: `${progress}%`, backgroundColor: color }]} />
    </View>
  </View>
);

interface ActionButtonProps {
  icon: string;
  label: string;
  onPress: () => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({ icon, label, onPress }) => (
  <TouchableOpacity style={styles.actionButton} onPress={onPress}>
    <MaterialCommunityIcons name={icon} size={28} color="#1F2937" />
    <Text style={styles.actionLabel}>{label}</Text>
  </TouchableOpacity>
);

export default function GooseDetailScreen() {
  const navigation = useNavigation();
  const [isLive, setIsLive] = useState(true);
  const [viewers, setViewers] = useState(128);

  const handleFeed = () => {
    console.log('喂食');
  };

  const handleWater = () => {
    console.log('喂水');
  };

  const handleClean = () => {
    console.log('清洁');
  };

  const handlePlay = () => {
    console.log('玩耍');
  };

  const handleGift = () => {
    console.log('转赠给朋友');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <MaterialCommunityIcons name="arrow-left" size={20} color="#1F2937" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>豆豆</Text>
          <TouchableOpacity>
            <MaterialCommunityIcons name="dots-horizontal" size={20} color="#1F2937" />
          </TouchableOpacity>
        </View>

        {/* Video Container */}
        <View style={styles.videoContainer}>
          <View style={styles.gooseDisplay}>
            <Text style={styles.gooseEmoji}>🦆</Text>
          </View>
          <View style={styles.videoControls}>
            <View style={styles.liveIndicator}>
              <MaterialCommunityIcons name="video" size={16} color="#fff" />
              <Text style={styles.liveText}>直播中</Text>
            </View>
            <View style={styles.viewerCount}>
              <MaterialCommunityIcons name="eye" size={16} color="#fff" />
              <Text style={styles.viewerText}>{viewers}</Text>
            </View>
          </View>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <StatCard
            icon="heart-pulse"
            label="健康"
            progress={85}
            color="#32CD32"
            gradientColors={['#98FB98', '#32CD32']}
          />
          <StatCard
            icon="food"
            label="饥饿"
            progress={60}
            color="#FF7F50"
            gradientColors={['#FFA07A', '#FF7F50']}
          />
          <StatCard
            icon="soap"
            label="清洁"
            progress={75}
            color="#4682B4"
            gradientColors={['#87CEEB', '#4682B4']}
          />
          <StatCard
            icon="emoticon-happy"
            label="心情"
            progress={90}
            color="#FFD700"
            gradientColors={['#FFD700', '#FFA500']}
          />
        </View>

        {/* Actions Grid */}
        <View style={styles.actionsGrid}>
          <ActionButton icon="bone" label="喂食" onPress={handleFeed} />
          <ActionButton icon="water" label="喂水" onPress={handleWater} />
          <ActionButton icon="shower" label="清洁" onPress={handleClean} />
          <ActionButton icon="gamepad-variant" label="玩耍" onPress={handlePlay} />
        </View>

        {/* Gift Button */}
        <TouchableOpacity style={styles.giftButton} onPress={handleGift}>
          <MaterialCommunityIcons name="gift" size={20} color="#1F2937" />
          <Text style={styles.giftButtonText}>转赠给朋友</Text>
        </TouchableOpacity>
      </View>

      <BottomNavigation />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    background: 'linear-gradient(180deg, #E3F2FD 0%, #FFFDE7 100%)',
    backgroundColor: '#E3F2FD', // Fallback
  },
  content: {
    flex: 1,
    padding: 24,
    paddingBottom: 100, // Space for bottom navigation
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1F2937',
    fontFamily: 'Inter',
  },
  videoContainer: {
    width: '100%',
    height: 250,
    borderRadius: 32,
    backgroundColor: '#333',
    marginBottom: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    position: 'relative',
  },
  gooseDisplay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#87CEEB',
  },
  gooseEmoji: {
    fontSize: 80,
    textAlign: 'center',
  },
  videoControls: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  liveText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  viewerCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewerText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    borderRadius: 32,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: '#1F2937',
    marginTop: 8,
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 10,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 5,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 20,
    paddingVertical: 16,
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1F2937',
  },
  giftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFD700',
    borderRadius: 20,
    paddingVertical: 16,
    gap: 8,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 8,
  },
  giftButtonText: {
    fontSize: 17,
    fontWeight: '700',
    color: '#1F2937',
  },
});

import React, { useState, useEffect } from 'react';
import { 
  View, 
  StyleSheet, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  SafeAreaView,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import { authApi } from '../../services/authApi';
import { PhoneLoginRequest, SmsCodeType } from '../../types/auth';
import { LoginScreenNavigationProp } from '../../navigation/types';
import { useAuth } from '../../context/AuthContext';
import { theme } from '../../theme';

const { width, height } = Dimensions.get('window');

export const NewLoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { login } = useAuth();

  // 状态管理
  const [phone, setPhone] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 手机号验证
  const validatePhone = (phone: string): boolean => /^1[3-9]\d{9}$/.test(phone);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 发送验证码
  const handleSendCode = async () => {
    if (!validatePhone(phone)) {
      setError('请输入正确的手机号格式');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      await authApi.sendSmsCodeV2({
        phone: phone,
        codeType: SmsCodeType.LOGIN,
      });
      setCountdown(60);
      Alert.alert('成功', '验证码发送成功');
    } catch (error: any) {
      setError(error.message || '验证码发送失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 登录处理
  const handleLogin = async () => {
    if (!validatePhone(phone)) {
      setError('请输入正确的手机号格式');
      return;
    }

    if (!verificationCode || verificationCode.length !== 6) {
      setError('请输入6位验证码');
      return;
    }

    setIsLoading(true);
    setError(null);

    const loginData: PhoneLoginRequest = {
      phone: phone,
      code: verificationCode,
      deviceInfo: 'React Native App'
    };

    try {
      await login.mutateAsync({ type: 'phone', data: loginData });
      // 登录成功后会自动导航到主页面
    } catch (error: any) {
      setError(error.message || '登录失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 一键手机号登录
  const handleOneClickLogin = () => {
    Alert.alert('一键登录', '此功能需要集成运营商SDK，暂未实现');
  };

  // 微信登录
  const handleWechatLogin = () => {
    Alert.alert('微信登录', '此功能需要集成微信SDK，暂未实现');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#F4F6F9" />
      
      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* 主要内容区域 */}
        <View style={styles.content}>
          {/* 登录卡片 */}
          <View style={styles.loginCard}>
            {/* 小鹅图标 */}
            <View style={styles.mascotContainer}>
              <Text style={styles.mascot}>🦆</Text>
            </View>

            {/* 输入区域 */}
            <View style={styles.inputContainer}>
              {/* 手机号输入行 */}
              <View style={styles.phoneInputRow}>
                <TextInput
                  style={[styles.phoneInput, error ? styles.inputError : null]}
                  placeholder="手机号"
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                  maxLength={11}
                  editable={!isLoading}
                />
                <TouchableOpacity
                  style={[
                    styles.codeButton,
                    (!validatePhone(phone) || countdown > 0 || isLoading) && styles.codeButtonDisabled
                  ]}
                  onPress={handleSendCode}
                  disabled={!validatePhone(phone) || countdown > 0 || isLoading}
                  accessibilityState={{ disabled: !validatePhone(phone) || countdown > 0 || isLoading }}
                >
                  <Text style={styles.codeButtonText}>
                    {countdown > 0 ? `${countdown}s` : '获取验证码'}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* 验证码输入 */}
              <TextInput
                style={[styles.codeInput, error ? styles.inputError : null]}
                placeholder="6位验证码"
                value={verificationCode}
                onChangeText={setVerificationCode}
                keyboardType="number-pad"
                maxLength={6}
                editable={!isLoading}
              />

              {/* 错误信息 */}
              {error && (
                <Text style={styles.errorText}>{error}</Text>
              )}

              {/* 登录按钮 */}
              <TouchableOpacity
                style={[
                  styles.loginButton,
                  (!validatePhone(phone) || !verificationCode || isLoading) && styles.loginButtonDisabled
                ]}
                onPress={handleLogin}
                disabled={!validatePhone(phone) || !verificationCode || isLoading}
                accessibilityState={{ disabled: !validatePhone(phone) || !verificationCode || isLoading }}
              >
                <Text style={styles.loginButtonText}>
                  {isLoading ? '登录中...' : '登录 / 注册'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* 分隔线 */}
          <View style={styles.dividerContainer}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>或</Text>
            <View style={styles.dividerLine} />
          </View>

          {/* 社交登录选项 */}
          <View style={styles.socialLoginContainer}>
            <TouchableOpacity style={styles.socialButton} onPress={handleOneClickLogin}>
              <MaterialCommunityIcons name="cellphone" size={20} color="#1F2937" />
              <Text style={styles.socialButtonText}>一键手机号登录</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.socialButton} onPress={handleWechatLogin}>
              <MaterialCommunityIcons name="wechat" size={20} color="#07C160" />
              <Text style={styles.socialButtonText}>微信登录</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 底部导航 */}
        <View style={styles.bottomNav}>
          <TouchableOpacity style={styles.navItem}>
            <MaterialCommunityIcons name="compass" size={18} color="#6B7280" />
            <Text style={styles.navLabel}>发现</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.navItem}>
            <MaterialCommunityIcons name="store" size={18} color="#6B7280" />
            <Text style={styles.navLabel}>商城</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.navItem}>
            <MaterialCommunityIcons name="egg" size={18} color="#6B7280" />
            <Text style={styles.navLabel}>我的鹅</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.navItem}>
            <MaterialCommunityIcons name="account-group" size={18} color="#6B7280" />
            <Text style={styles.navLabel}>动态</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.navItem}>
            <MaterialCommunityIcons name="account" size={18} color="#6B7280" />
            <Text style={styles.navLabel}>我的</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E0F2FE', // 浅蓝色背景，更接近设计图
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loginCard: {
    width: width * 0.85,
    backgroundColor: '#FFFFFF',
    borderRadius: 40,
    padding: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 10,
    borderWidth: 8,
    borderColor: '#1F2937', // 深灰边框
    marginBottom: 20,
  },
  mascotContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  mascot: {
    fontSize: 80,
    lineHeight: 80,
  },
  inputContainer: {
    width: '100%',
  },
  phoneInputRow: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  phoneInput: {
    flex: 1,
    height: 44,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  codeButton: {
    height: 44,
    backgroundColor: '#87CEEB', // 天空蓝
    borderRadius: 20,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    shadowColor: '#192832',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  codeButtonDisabled: {
    backgroundColor: '#D1D5DB',
    shadowOpacity: 0,
    elevation: 0,
  },
  codeButtonText: {
    color: '#1F2937',
    fontSize: 14,
    fontWeight: 'bold',
  },
  codeInput: {
    width: '100%',
    height: 44,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputError: {
    borderColor: '#EF4444',
    borderWidth: 1,
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  loginButton: {
    width: '100%',
    height: 44,
    backgroundColor: '#FFD700', // 金黄色
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#192832',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loginButtonDisabled: {
    backgroundColor: '#D1D5DB',
    shadowOpacity: 0,
    elevation: 0,
  },
  loginButtonText: {
    color: '#1F2937',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 32,
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'space-around',
    alignItems: 'center',
    shadowColor: '#192832',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  navItem: {
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  navLabel: {
    fontSize: 9,
    color: '#6B7280',
    marginTop: 4,
    fontFamily: 'Varela Round',
  },
  // 新增样式
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E5E7EB',
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  socialLoginContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E0F2FE', // 浅蓝色背景
    borderRadius: 20,
    paddingVertical: 14,
    paddingHorizontal: 20,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  socialButtonText: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '500',
  },
});

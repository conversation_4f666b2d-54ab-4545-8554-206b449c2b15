import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

import { authApi } from '../../services/authApi';
import { LoginRequest, PhoneLoginRequest, PhonePasswordLoginRequest, SmsCodeType } from '../../types/auth';
import { LoginScreenNavigationProp } from '../../navigation/types';
import { useAuth } from '../../context/AuthContext';
import { theme } from '../../theme';
import LoadingIndicator from '../../components/LoadingIndicator';
import ErrorMessage from '../../components/ErrorMessage';

enum LoginMode {
  USERNAME = 'username',
  PHONE_SMS = 'phone_sms',
  PHONE_PASSWORD = 'phone_password',
}

export const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { login, state } = useAuth();

  const [loginMode, setLoginMode] = useState<LoginMode>(LoginMode.USERNAME);
  const [credentials, setCredentials] = useState<LoginRequest>({ username: '', password: '' });
  const [phoneCredentials, setPhoneCredentials] = useState<PhoneLoginRequest>({ phone: '', code: '', deviceInfo: 'React Native App' });
  const [phonePasswordCredentials, setPhonePasswordCredentials] = useState<PhonePasswordLoginRequest>({ phone: '', password: '', deviceInfo: 'React Native App' });
  const [countdown, setCountdown] = useState(0);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const validatePhone = (phone: string): boolean => /^1[3-9]\d{9}$/.test(phone);

  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleLogin = () => {
    setSuccessMessage(null);
    login.reset(); // 重置上一次的错误状态

    if (loginMode === LoginMode.USERNAME) {
      login.mutate({ type: 'username', data: credentials });
    } else if (loginMode === LoginMode.PHONE_SMS) {
      login.mutate({ type: 'phone', data: phoneCredentials });
    } else if (loginMode === LoginMode.PHONE_PASSWORD) {
      login.mutate({ type: 'phonePassword', data: phonePasswordCredentials });
    }
  };

  const handleSendSmsCode = async () => {
    if (!validatePhone(phoneCredentials.phone)) {
        // Ideally, have a local form error state. For now, AuthContext will catch the API error.
      return;
    }
    setSuccessMessage(null);
    try {
      await authApi.sendSmsCodeV2({
        phone: phoneCredentials.phone,
        codeType: SmsCodeType.LOGIN,
      });
      setSuccessMessage('验证码发送成功');
      startCountdown();
    } catch (error: any) {
      // The global error state in AuthContext can be used if we pipe this through it.
      // For now, a local message is also fine for non-blocking errors.
    }
  };

  return (
    <View style={styles.container}>
      <Card>
        <Text style={styles.title}>🦆 小鹅养成APP</Text>
        <Text style={styles.subtitle}>选择登录方式</Text>

        <View style={styles.segmentedButtonsContainer}>
          <Button
            title="用户名"
            onPress={() => setLoginMode(LoginMode.USERNAME)}
            style={[styles.segmentedButton, loginMode === LoginMode.USERNAME && styles.segmentedButtonActive]}
            textStyle={[styles.segmentedButtonText, loginMode === LoginMode.USERNAME && styles.segmentedButtonTextActive]}
          />
          <Button
            title="手机+密码"
            onPress={() => setLoginMode(LoginMode.PHONE_PASSWORD)}
            style={[styles.segmentedButton, loginMode === LoginMode.PHONE_PASSWORD && styles.segmentedButtonActive]}
            textStyle={[styles.segmentedButtonText, loginMode === LoginMode.PHONE_PASSWORD && styles.segmentedButtonTextActive]}
          />
          <Button
            title="验证码"
            onPress={() => setLoginMode(LoginMode.PHONE_SMS)}
            style={[styles.segmentedButton, loginMode === LoginMode.PHONE_SMS && styles.segmentedButtonActive]}
            textStyle={[styles.segmentedButtonText, loginMode === LoginMode.PHONE_SMS && styles.segmentedButtonTextActive]}
          />
        </View>

        <View style={styles.divider} />

        {loginMode === LoginMode.USERNAME && (
          <View>
            <TextInput
              placeholder="用户名"
              value={credentials.username}
              onChangeText={(text) => setCredentials(prev => ({ ...prev, username: text }))}
              style={styles.input}
              editable={!login.isPending}
            />
            <TextInput
              placeholder="密码"
              value={credentials.password}
              onChangeText={(text) => setCredentials(prev => ({ ...prev, password: text }))}
              secureTextEntry
              style={styles.input}
              editable={!login.isPending}
            />
          </View>
        )}

        {loginMode === LoginMode.PHONE_PASSWORD && (
           <View>
             <TextInput
               placeholder="手机号"
               value={phonePasswordCredentials.phone}
               onChangeText={(text) => setPhonePasswordCredentials(prev => ({ ...prev, phone: text }))}
               style={styles.input}
               editable={!login.isPending}
               keyboardType="phone-pad"
               maxLength={11}
             />
             <TextInput
               placeholder="密码"
               value={phonePasswordCredentials.password}
               onChangeText={(text) => setPhonePasswordCredentials(prev => ({ ...prev, password: text }))}
               secureTextEntry
               style={styles.input}
               editable={!login.isPending}
             />
           </View>
        )}

        {loginMode === LoginMode.PHONE_SMS && (
          <View>
            <TextInput
              placeholder="手机号"
              value={phoneCredentials.phone}
              onChangeText={(text) => setPhoneCredentials(prev => ({ ...prev, phone: text }))}
              style={styles.input}
              editable={!login.isPending}
              keyboardType="phone-pad"
              maxLength={11}
            />
            <View style={styles.codeInputContainer}>
              <TextInput
                placeholder="6位验证码"
                value={phoneCredentials.code}
                onChangeText={(text) => setPhoneCredentials(prev => ({ ...prev, code: text }))}
                style={[styles.input, styles.codeInput]}
                editable={!login.isPending}
                keyboardType="number-pad"
                maxLength={6}
              />
              <Button
                title={countdown > 0 ? `${countdown}s` : '获取验证码'}
                onPress={handleSendSmsCode}
                disabled={login.isPending || countdown > 0 || !phoneCredentials.phone}
                style={styles.codeButton}
              />
            </View>
          </View>
        )}

        {login.isPending && <LoadingIndicator />}
        <ErrorMessage message={login.error?.message || null} />
        {successMessage && <Text style={styles.successText}>{successMessage}</Text>}
        
        <Button
            title={loginMode === LoginMode.USERNAME ? '登录' : '手机号登录'}
            onPress={handleLogin}
            disabled={login.isPending}
            style={styles.button}
          />
          
        <View style={styles.divider} />

        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>还没有账号？</Text>
          <Button
            title="手机号注册"
            onPress={() => navigation.navigate('MobileRegistration')}
            style={styles.registerButton}
            textStyle={styles.registerButtonText}
          />
        </View>

      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.background,
  },
  title: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.xxl,
    fontWeight: '700',
    color: theme.colors.gooseYellow,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.lg,
  },
  segmentedButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.lg,
  },
  segmentedButton: {
    flex: 1,
    backgroundColor: theme.colors.background,
    borderColor: theme.colors.gooseYellow,
    borderWidth: 1,
  },
  segmentedButtonActive: {
    backgroundColor: theme.colors.gooseYellow,
  },
  segmentedButtonText: {
    color: theme.colors.gooseYellow,
  },
  segmentedButtonTextActive: {
    color: '#FFFFFF',
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.textSecondary,
    opacity: 0.2,
    marginVertical: theme.spacing.lg,
  },
  input: {
    marginBottom: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.textSecondary,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.fonts.sizes.md,
    backgroundColor: '#fff',
  },
  codeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  codeInput: {
    flex: 1,
    marginBottom: 0,
  },
  codeButton: {
    paddingVertical: 4,
  },
  button: {
    marginTop: theme.spacing.md,
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.lg,
  },
  registerText: {
    color: theme.colors.textSecondary,
    fontSize: theme.fonts.sizes.sm,
  },
  registerButton: {
    backgroundColor: 'transparent',
    padding: 0,
    marginLeft: theme.spacing.xs,
  },
  registerButtonText: {
    color: theme.colors.gooseYellow,
    fontWeight: 'bold',
  },
  successText: {
    color: theme.colors.successGreen,
    textAlign: 'center',
    marginVertical: theme.spacing.sm,
  },
});

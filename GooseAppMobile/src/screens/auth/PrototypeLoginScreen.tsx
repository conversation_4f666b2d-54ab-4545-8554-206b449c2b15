import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import BottomNavigation from '../../components/BottomNavigation';
import { useAuth } from '../../context/AuthContext';
import { PhoneLoginRequest, SmsCodeType } from '../../types/auth';
import { authApi } from '../../services/authApi';

const { width, height } = Dimensions.get('window');

export default function PrototypeLoginScreen() {
  const navigation = useNavigation();
  const { login } = useAuth();
  const [phone, setPhone] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 手机号验证函数（支持测试模式）
  const validatePhone = (phoneNumber: string) => {
    // 正式手机号格式：1开头，第二位3-9，总共11位
    const phoneRegex = /^1[3-9]\d{9}$/;

    // 测试模式：允许3位以上的数字（用于开发测试）
    const testPhoneRegex = /^\d{3,}$/;

    return phoneRegex.test(phoneNumber) || testPhoneRegex.test(phoneNumber);
  };

  // 调试信息
  const isPhoneValid = validatePhone(phone);
  const hasCode = verificationCode.trim().length > 0;
  const isButtonEnabled = isPhoneValid && hasCode && !isLoading;

  console.log('Debug Info:', {
    phone,
    verificationCode,
    isPhoneValid,
    hasCode,
    isLoading,
    isButtonEnabled,
    phoneLength: phone.length,
    codeLength: verificationCode.length
  });

  const handleGetCode = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号格式');
      return;
    }

    try {
      await authApi.sendSmsCodeV2({
        phone: phone,
        codeType: SmsCodeType.LOGIN,
      });
      Alert.alert('验证码已发送', `验证码已发送到 ${phone}\n\n提示：可以使用特殊验证码 000000 进行测试`);
    } catch (error: any) {
      console.error('发送验证码错误:', error);
      Alert.alert('发送失败', error.message || '验证码发送失败，请重试');
    }
  };

  const handleLogin = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号格式');
      return;
    }

    if (!verificationCode) {
      Alert.alert('提示', '请输入验证码');
      return;
    }

    // 允许特殊验证码或6位验证码
    if (verificationCode !== '000000' && verificationCode.length !== 6) {
      Alert.alert('提示', '请输入6位验证码');
      return;
    }

    setIsLoading(true);

    const loginData: PhoneLoginRequest = {
      phone: phone,
      code: verificationCode,
      deviceInfo: 'React Native App'
    };

    try {
      await login.mutateAsync({ type: 'phone', data: loginData });
      Alert.alert('登录成功', '欢迎使用小鹅养成APP！');
      // 登录成功后，AuthContext会自动更新状态，应用会导航到主流程
    } catch (error: any) {
      console.error('登录错误:', error);
      Alert.alert('登录失败', error.message || '登录失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOneClickLogin = () => {
    Alert.alert('一键登录', '此功能需要集成运营商SDK，暂未实现');
  };

  const handleWechatLogin = () => {
    Alert.alert('微信登录', '此功能需要集成微信SDK，暂未实现');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      <View style={styles.content}>
        <View style={styles.authContainer}>
          {/* 小鹅吉祥物 */}
          <View style={styles.mascotContainer}>
            <Text style={styles.mascot}>🦆</Text>
          </View>
          
          {/* 登录面板 */}
          <View style={styles.authPanel}>
            <View style={styles.inputGroup}>
              <TextInput
                style={styles.inputField}
                placeholder="手机号"
                placeholderTextColor="#9CA3AF"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                maxLength={11}
              />
              <TouchableOpacity 
                style={styles.getCodeButton} 
                onPress={handleGetCode}
                disabled={!validatePhone(phone)}
              >
                <Text style={styles.getCodeButtonText}>获取验证码</Text>
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={[styles.inputField, styles.codeInput]}
              placeholder="6位验证码"
              placeholderTextColor="#9CA3AF"
              value={verificationCode}
              onChangeText={setVerificationCode}
              keyboardType="number-pad"
              maxLength={6}
            />

            <TouchableOpacity
              style={[
                styles.mainAuthButton,
                !isButtonEnabled && styles.buttonDisabled
              ]}
              onPress={handleLogin}
              disabled={!isButtonEnabled}
            >
              <Text style={styles.mainAuthButtonText}>
                {isLoading ? '登录中...' : '登录 / 注册'}
              </Text>
            </TouchableOpacity>

            {/* 调试按钮 */}
            <TouchableOpacity
              style={[styles.mainAuthButton, { backgroundColor: '#FF6B6B', marginTop: 8 }]}
              onPress={() => {
                Alert.alert('调试信息',
                  `手机号: ${phone} (长度: ${phone.length})\n` +
                  `验证码: ${verificationCode} (长度: ${verificationCode.length})\n` +
                  `手机号有效: ${isPhoneValid}\n` +
                  `有验证码: ${hasCode}\n` +
                  `加载中: ${isLoading}\n` +
                  `按钮状态: ${isButtonEnabled ? '可用' : '禁用'}`
                );
              }}
            >
              <Text style={styles.mainAuthButtonText}>调试信息</Text>
            </TouchableOpacity>
          </View>

          {/* 分隔线 */}
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>或</Text>
            <View style={styles.dividerLine} />
          </View>

          {/* 社交登录选项 */}
          <View style={styles.socialLoginOptions}>
            <TouchableOpacity style={styles.socialButton} onPress={handleOneClickLogin}>
              <MaterialCommunityIcons name="cellphone" size={20} color="#1F2937" />
              <Text style={styles.socialButtonText}>一键手机号登录</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.socialButton} onPress={handleWechatLogin}>
              <MaterialCommunityIcons name="wechat" size={20} color="#07C160" />
              <Text style={styles.socialButtonText}>微信登录</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      <BottomNavigation />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E3F2FD', // 浅蓝色背景
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    paddingBottom: 100, // 为底部导航留空间
  },
  authContainer: {
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
  },
  mascotContainer: {
    marginBottom: 24,
  },
  mascot: {
    fontSize: 100,
    textAlign: 'center',
  },
  authPanel: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 32,
    padding: 32,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    gap: 24,
  },
  inputGroup: {
    flexDirection: 'row',
    gap: 8,
  },
  codeInput: {
    marginBottom: 8,
  },
  inputField: {
    flex: 1,
    paddingVertical: 18,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 20,
    fontSize: 17,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: 'transparent',
    fontFamily: 'Varela Round',
    minHeight: 56,
  },
  getCodeButton: {
    backgroundColor: '#87CEEB',
    borderRadius: 20,
    paddingHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  getCodeButtonText: {
    color: '#1F2937',
    fontSize: 15,
    fontWeight: '700',
    fontFamily: 'Inter',
  },
  mainAuthButton: {
    backgroundColor: '#FFD700',
    borderRadius: 20,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    minHeight: 56,
  },
  mainAuthButtonText: {
    color: '#1F2937',
    fontSize: 17,
    fontWeight: '700',
    fontFamily: 'Inter',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 13,
    color: '#9CA3AF',
    fontFamily: 'Varela Round',
  },
  socialLoginOptions: {
    width: '100%',
    gap: 16,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E3F2FD',
    borderRadius: 20,
    paddingVertical: 14,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  socialButtonText: {
    color: '#1F2937',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Varela Round',
  },
});

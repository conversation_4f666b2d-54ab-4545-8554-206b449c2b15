import React, { useState, useCallback } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, Button, TouchableOpacity, Image, SafeAreaView, StatusBar } from 'react-native';
import { Text, Card, Title, Paragraph, ActivityIndicator, ProgressBar, Chip } from 'react-native-paper';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { theme } from '../../theme';
import { Goose } from '../../types/goose';
import { useGoose } from '../../context/GooseContext'; // Import the context hook
import BottomNavigation from '../../components/BottomNavigation';

// Helper function to get color based on value
const getStatusColor = (value: number): string => {
  if (value > 70) return theme.colors.successGreen;
  if (value > 30) return theme.colors.warningOrange;
  return theme.colors.errorRed;
};

export const MyGeeseScreen: React.FC = () => {
  // Consume state and actions from GooseContext
  const { state, fetchMyGeese, setCurrentGoose } = useGoose();
  const { myGeese, loading, error } = state;
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation();

  // When the screen comes into focus, fetch the geese list.
  useFocusEffect(
    useCallback(() => {
      fetchMyGeese();
    }, [fetchMyGeese])
  );
  
  // Handle refreshing state locally but trigger global fetch
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchMyGeese();
    setRefreshing(false);
  }, [fetchMyGeese]);

  if (loading && myGeese.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
        <View style={styles.centered}>
          <ActivityIndicator animating={true} size="large" color={theme.colors.gooseYellow} />
          <Text style={styles.loadingText}>正在呼唤您的小鹅...</Text>
        </View>
        <BottomNavigation />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
        <View style={styles.centered}>
          <Text style={styles.errorText}>{error}</Text>
          <Button title="重试" onPress={fetchMyGeese} />
        </View>
        <BottomNavigation />
      </SafeAreaView>
    );
  }

  const renderGoose = ({ item }: { item: Goose }) => {
    const getGenderSymbol = (gender: number | null) => {
      if (gender === 1) return '♂'; // Male
      if (gender === 2) return '♀'; // Female
      return '?'; // Unknown
    };
    
    // Fallback to 0 for any nullish progress value
    const health = item.health ?? 0;
    const happiness = item.happiness ?? 0;
    const experience = item.experience ?? 0;
    const expToNextLevel = item.expToNextLevel ?? 100;

    return (
      <TouchableOpacity onPress={() => {
        console.log('Navigate to goose:', item.id);
        // 设置当前小鹅并导航到详情页
        setCurrentGoose(item);
        navigation.navigate('GooseDetail' as never, { gooseId: item.id } as never);
      }}>
        <Card style={styles.card}>
          <View style={styles.cardLayout}>
            {/* Left Side: Avatar */}
            <Image
              source={{ uri: item.imageUrl || 'https://wwrmac.oss-cn-beijing.aliyuncs.com/e_avatar.png?Expires=**********&OSSAccessKeyId=TMP.3KmXQkavpoz5dDVwQoKeFW8f1MnTaxWb8sAZXdMbkWQLCTWmxhKqvz37SDLUp2WDA1VxAwvdC2JcBQoDZqj6TusDZ5FAE9&Signature=4LzADTx5pqvcg9aGk4OH3nnq5Xw%3D' }}
              style={styles.avatar}
            />

            {/* Right Side: Info */}
            <View style={styles.infoContainer}>
              <View style={styles.titleContainer}>
                <Title style={styles.gooseName}>{item.name || '未命名的小鹅'} (Lv. {item.level ?? 1})</Title>
                {item.needsCare && (
                  <Chip icon="bell" style={styles.needsCareChip} textStyle={styles.needsCareChipText}>
                    照顾
                  </Chip>
                )}
              </View>
              <Paragraph style={styles.breedText}>
                {getGenderSymbol(item.gender)} {item.breed || '未知品种'} • {item.age ?? 0}周
              </Paragraph>

              <View style={styles.statusContainer}>
                <Text style={styles.statusLabel}>健康</Text>
                <ProgressBar progress={health / 100} color={getStatusColor(health)} style={styles.progressBar} />
                <Text style={styles.statusValue}>{health}%</Text>
              </View>
              <View style={styles.statusContainer}>
                <Text style={styles.statusLabel}>快乐</Text>
                <ProgressBar progress={happiness / 100} color={getStatusColor(happiness)} style={styles.progressBar} />
                <Text style={styles.statusValue}>{happiness}%</Text>
              </View>
              <View style={styles.statusContainer}>
                <Text style={styles.statusLabel}>经验</Text>
                <ProgressBar progress={experience / expToNextLevel} color={theme.colors.gooseYellow} style={styles.progressBar} />
                <Text style={styles.statusValue}>{experience}/{expToNextLevel}</Text>
              </View>
            </View>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <FlatList
        style={styles.content}
        data={myGeese}
        renderItem={renderGoose}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={<Title style={styles.header}>我 的 小 鹅</Title>}
        ListEmptyComponent={
          <View style={styles.centered}>
            <Text style={styles.emptyText}>您还没有小鹅，快去商城领养一只吧！</Text>
          </View>
        }
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[theme.colors.gooseYellow]} />
        }
        contentContainerStyle={styles.listContentContainer}
        showsVerticalScrollIndicator={false}
      />

      <BottomNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E3F2FD',
  },
  content: {
    flex: 1,
    backgroundColor: '#E3F2FD',
  },
  listContentContainer: {
    paddingBottom: 100,
    paddingHorizontal: theme.spacing.md,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 150,
  },
  card: {
    marginVertical: theme.spacing.sm,
    elevation: 3,
    borderRadius: 16,
    backgroundColor: '#fff',
  },
  cardLayout: {
    flexDirection: 'row',
    padding: theme.spacing.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40, // Half of width/height to make it a circle
    marginRight: theme.spacing.md,
    backgroundColor: theme.colors.neutralGray, // Placeholder color
  },
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  gooseName: {
    fontWeight: 'bold',
    fontSize: 18,
    flexShrink: 1, // Allow name to wrap if too long
  },
  needsCareChip: {
    backgroundColor: theme.colors.warningOrange,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  needsCareChipText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 10,
  },
  breedText: {
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  statusLabel: {
    width: 40,
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
  },
  statusValue: {
    width: 45,
    textAlign: 'right',
    fontSize: 12,
    color: theme.colors.textPrimary,
    fontWeight: '600',
  },
  header: {
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 4,
    color: theme.colors.textPrimary,
  },
  loadingText: {
    marginTop: 10,
    color: theme.colors.textSecondary,
  },
  errorText: {
    color: theme.colors.errorRed,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptyText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
  }
});

export default MyGeeseScreen;
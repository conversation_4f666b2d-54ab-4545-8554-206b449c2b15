import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation, useRoute } from '@react-navigation/native';

const { width } = Dimensions.get('window');

interface NavItemProps {
  icon: string;
  label: string;
  isActive: boolean;
  onPress: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, isActive, onPress }) => (
  <TouchableOpacity style={styles.navItem} onPress={onPress}>
    <MaterialCommunityIcons 
      name={icon} 
      size={22} 
      color={isActive ? '#FFD700' : '#6B7280'} 
    />
    <Text style={[styles.navLabel, { color: isActive ? '#FFD700' : '#6B7280' }]}>
      {label}
    </Text>
    {isActive && <View style={styles.activeIndicator} />}
  </TouchableOpacity>
);

export default function BottomNavigation() {
  const navigation = useNavigation();
  const route = useRoute();

  const navItems = [
    {
      icon: 'compass-outline',
      label: '发现',
      routeName: 'Discover',
      onPress: () => navigation.navigate('Discover' as never),
    },
    {
      icon: 'store-outline',
      label: '商城',
      routeName: 'Shop',
      onPress: () => navigation.navigate('Shop' as never),
    },
    {
      icon: 'egg-outline',
      label: '我的鹅',
      routeName: 'MyGeese',
      onPress: () => navigation.navigate('MyGeese' as never),
    },
    {
      icon: 'account-group-outline',
      label: '动态',
      routeName: 'SocialFeed',
      onPress: () => navigation.navigate('SocialFeed' as never),
    },
    {
      icon: 'account-circle-outline',
      label: '我的',
      routeName: 'Profile',
      onPress: () => navigation.navigate('Profile' as never),
    },
  ];

  return (
    <View style={styles.container}>
      {navItems.map((item, index) => (
        <NavItem
          key={index}
          icon={item.icon}
          label={item.label}
          isActive={route.name === item.routeName}
          onPress={item.onPress}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    height: 70,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 32,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    zIndex: 1000,
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    paddingVertical: 8,
    paddingHorizontal: 8,
    position: 'relative',
  },
  navLabel: {
    fontSize: 11,
    fontWeight: '500',
    fontFamily: 'Varela Round',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -2,
    width: 24,
    height: 4,
    backgroundColor: '#FFD700',
    borderRadius: 2,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 4,
  },
});

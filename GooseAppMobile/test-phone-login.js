#!/usr/bin/env node

// 测试手机号验证码登录API
const http = require('http');

const API_BASE = 'http://localhost:8080/api/v1/auth';

// 测试数据
const testPhone = '13800138000'; // 使用标准手机号格式
const testCode = '000000'; // 特殊验证码，6位

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testPhoneLogin() {
  console.log('🧪 测试手机号验证码登录API');
  console.log('================================');

  try {
    // 1. 测试发送验证码
    console.log('\n1. 发送验证码...');
    const sendCodeOptions = {
      hostname: 'localhost',
      port: 8080,
      path: '/api/v1/auth/send-sms-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const sendCodeData = {
      phone: testPhone,
      codeType: 1 // 登录验证码
    };

    const sendCodeResult = await makeRequest(sendCodeOptions, sendCodeData);
    console.log('发送验证码结果:', sendCodeResult);

    // 2. 测试手机号验证码登录
    console.log('\n2. 手机号验证码登录...');
    const loginOptions = {
      hostname: 'localhost',
      port: 8080,
      path: '/api/v1/auth/login-by-phone',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const loginData = {
      phone: testPhone,
      code: testCode,
      deviceInfo: 'Test iPhone iOS 17.5'
    };

    const loginResult = await makeRequest(loginOptions, loginData);
    console.log('登录结果:', loginResult);

    if (loginResult.status === 200 && loginResult.data.success) {
      console.log('\n✅ 登录成功！');
      console.log('Token:', loginResult.data.data.token);
      console.log('用户信息:', {
        userId: loginResult.data.data.userId,
        username: loginResult.data.data.username,
        nickname: loginResult.data.data.nickname
      });
    } else {
      console.log('\n❌ 登录失败！');
      console.log('错误信息:', loginResult.data.message || '未知错误');
    }

  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testPhoneLogin();

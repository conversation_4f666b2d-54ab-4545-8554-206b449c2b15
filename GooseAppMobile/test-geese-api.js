#!/usr/bin/env node

/**
 * 测试小鹅API
 * 使用已有的token测试小鹅相关接口
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8080/api/v1';
// 使用之前登录获得的token
const TOKEN = 'd9fe74e4-6680-4c90-82fa-07a41da3c199';

async function testGeeseAPI() {
  console.log('🧪 测试小鹅API');
  console.log('================================\n');

  try {
    // 1. 测试获取用户小鹅列表
    console.log('1. 测试获取用户小鹅列表...');
    const geeseResponse = await fetch(`${BASE_URL}/geese/my`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TOKEN}`,
        'Content-Type': 'application/json',
      },
    });

    const geeseResult = await geeseResponse.json();
    console.log('小鹅列表结果:', {
      status: geeseResponse.status,
      data: geeseResult
    });

    if (!geeseResponse.ok || !geeseResult.success) {
      throw new Error(`获取小鹅列表失败: ${geeseResult.message}`);
    }

    console.log(`✅ 小鹅数量: ${geeseResult.data.length}`);
    
    if (geeseResult.data.length > 0) {
      const firstGoose = geeseResult.data[0];
      console.log('第一只小鹅:', firstGoose);

      // 2. 测试获取小鹅详情
      console.log('\n2. 测试获取小鹅详情...');
      const detailResponse = await fetch(`${BASE_URL}/geese/${firstGoose.id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
        },
      });

      const detailResult = await detailResponse.json();
      console.log('小鹅详情结果:', {
        status: detailResponse.status,
        data: detailResult
      });

      if (detailResponse.ok && detailResult.success) {
        console.log('✅ 小鹅详情获取成功');
        console.log('小鹅详情:', detailResult.data);
      }

      // 3. 测试互动操作（喂食）
      console.log('\n3. 测试喂食操作...');
      const feedResponse = await fetch(`${BASE_URL}/geese/${firstGoose.id}/feed`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
        },
      });

      const feedResult = await feedResponse.json();
      console.log('喂食结果:', {
        status: feedResponse.status,
        data: feedResult
      });

      if (feedResponse.ok && feedResult.success) {
        console.log('✅ 喂食操作成功');
      }
    } else {
      console.log('⚠️ 用户暂无小鹅，需要先购买小鹅');
    }

    // 4. 测试获取用户统计
    console.log('\n4. 测试获取用户统计...');
    const statsResponse = await fetch(`${BASE_URL}/geese/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TOKEN}`,
        'Content-Type': 'application/json',
      },
    });

    const statsResult = await statsResponse.json();
    console.log('用户统计结果:', {
      status: statsResponse.status,
      data: statsResult
    });

    if (statsResponse.ok && statsResult.success) {
      console.log('✅ 用户统计获取成功');
      console.log('统计信息:', statsResult.data);
    }

    console.log('\n✅ 小鹅API测试完成！');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testGeeseAPI();

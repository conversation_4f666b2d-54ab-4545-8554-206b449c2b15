# 小鹅养成APP - Figma组件规范

## 🎨 设计Token规范

### 颜色Token
```css
/* 主色调 */
--primary-yellow: #FFD700;
--primary-orange: #FFA500;
--primary-pink: #FFB6C1;

/* 中性色 */
--text-primary: #2C2C2C;
--text-secondary: #666666;
--text-disabled: #CCCCCC;
--background: #FFFFFF;
--surface: #F8F9FA;

/* 状态色 */
--success: #4CAF50;
--warning: #FF9800;
--error: #F44336;
--info: #2196F3;

/* 玻璃拟态效果 */
--glass-bg: rgba(255, 255, 255, 0.25);
--glass-border: rgba(255, 255, 255, 0.18);
--glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
```

### 字体Token
```css
/* 字体大小 */
--font-size-display: 24px;
--font-size-title: 20px;
--font-size-subtitle: 18px;
--font-size-body-large: 16px;
--font-size-body-medium: 14px;
--font-size-body-small: 12px;
--font-size-caption: 10px;

/* 字体粗细 */
--font-weight-bold: 700;
--font-weight-semibold: 600;
--font-weight-medium: 500;
--font-weight-regular: 400;

/* 行高 */
--line-height-tight: 1.2;
--line-height-normal: 1.4;
--line-height-loose: 1.6;
```

### 间距Token
```css
--spacing-xs: 4px;
--spacing-s: 8px;
--spacing-m: 16px;
--spacing-l: 24px;
--spacing-xl: 32px;
--spacing-xxl: 48px;
```

### 圆角Token
```css
--radius-small: 8px;
--radius-medium: 12px;
--radius-large: 16px;
--radius-xlarge: 24px;
--radius-round: 50%;
```

## 🧩 核心组件规范

### 1. Button组件

#### Primary Button
```
尺寸: 最小宽度120px, 高度48px
背景: linear-gradient(135deg, #FFD700, #FFA500)
文字: 16px, Medium, #FFFFFF
圆角: 12px
阴影: 0 4px 12px rgba(255, 215, 0, 0.3)
触摸区域: 最小44px x 44px

状态变体:
- Default: 正常状态
- Hover: 背景亮度+10%
- Pressed: 背景亮度-10%, 阴影减少
- Disabled: 背景#CCCCCC, 文字#999999
```

#### Secondary Button
```
尺寸: 最小宽度120px, 高度48px
背景: transparent
边框: 2px solid #FFD700
文字: 16px, Medium, #FFD700
圆角: 12px
```

#### Icon Button
```
尺寸: 44px x 44px (圆形)
背景: rgba(255, 215, 0, 0.1)
图标: 24px, #FFD700
圆角: 50%
```

### 2. Input组件

#### Text Input
```
尺寸: 宽度100%, 高度48px
背景: #FFFFFF
边框: 1px solid #E0E0E0
文字: 16px, Regular, #2C2C2C
占位符: 16px, Regular, #999999
圆角: 8px
内边距: 12px 16px

状态变体:
- Default: 边框#E0E0E0
- Focused: 边框#FFD700, 阴影0 0 0 3px rgba(255, 215, 0, 0.1)
- Error: 边框#F44336
- Disabled: 背景#F5F5F5, 文字#CCCCCC
```

#### Search Input
```
基于Text Input
左侧图标: 搜索图标, 20px, #666666
右侧图标: 清除图标, 16px, #999999 (可选)
```

### 3. Card组件

#### Goose Card
```
尺寸: 宽度100%, 最小高度120px
背景: 玻璃拟态效果
圆角: 16px
内边距: 16px
阴影: 0 8px 32px rgba(31, 38, 135, 0.37)

内容结构:
- 头像: 64px x 64px, 圆形
- 名称: 18px, SemiBold, #2C2C2C
- 状态: 14px, Regular, #666666
- 操作按钮: 两个并排的小按钮
```

#### Product Card
```
尺寸: 宽度(50% - 8px), 高度200px
背景: #FFFFFF
圆角: 12px
阴影: 0 2px 8px rgba(0, 0, 0, 0.1)

内容结构:
- 图片: 100% x 120px, 圆角12px 12px 0 0
- 名称: 14px, Medium, #2C2C2C
- 价格: 16px, Bold, #FFD700
- 标签: 小标签, 可选
```

### 4. Navigation组件

#### Bottom Navigation
```
尺寸: 宽度100%, 高度80px (包含安全区域)
背景: 玻璃拟态效果
位置: 固定在底部

Tab项目:
- 尺寸: 等宽分布, 高度56px
- 图标: 24px, 未选中#999999, 选中#FFD700
- 文字: 10px, Medium, 未选中#999999, 选中#FFD700
- 激活指示器: 2px高度的黄色条
```

#### Top Navigation
```
尺寸: 宽度100%, 高度56px
背景: 透明或玻璃拟态
内边距: 0 16px

内容结构:
- 左侧: 返回按钮或菜单按钮
- 中间: 页面标题, 18px, SemiBold
- 右侧: 操作按钮(可选)
```

### 5. Status组件

#### Health Indicator
```
尺寸: 宽度100%, 高度32px
背景: #F0F0F0
圆角: 16px

进度条:
- 高度: 100%
- 圆角: 16px
- 颜色: 根据数值变化
  - 80-100%: #4CAF50 (绿色)
  - 50-79%: #FF9800 (橙色)
  - 0-49%: #F44336 (红色)

文字: 12px, Medium, #FFFFFF, 居中
```

#### Badge
```
尺寸: 最小20px x 20px
背景: #F44336
文字: 10px, Bold, #FFFFFF
圆角: 50%
位置: 相对定位, 右上角
```

### 6. Modal组件

#### Dialog
```
尺寸: 最大宽度320px, 自适应高度
背景: #FFFFFF
圆角: 16px
阴影: 0 16px 48px rgba(0, 0, 0, 0.3)
内边距: 24px

内容结构:
- 标题: 20px, SemiBold, #2C2C2C
- 内容: 16px, Regular, #666666
- 按钮区域: 两个按钮, 右对齐
```

#### Bottom Sheet
```
尺寸: 宽度100%, 自适应高度
背景: #FFFFFF
圆角: 16px 16px 0 0
位置: 从底部滑入
```

## 📱 移动端适配规范

### 触摸目标
- 最小触摸区域: 44px x 44px
- 按钮间距: 最小8px
- 重要操作: 48px x 48px

### 字体大小
- 最小可读字体: 12px
- 主要内容: 16px
- 标题: 18px+

### 安全区域
- 顶部安全区域: 44px (刘海屏)
- 底部安全区域: 34px (Home指示器)
- 侧边安全区域: 16px

### 响应式断点
- 小屏: 320px - 374px
- 标准: 375px - 413px
- 大屏: 414px+

## 🎯 组件使用指南

### 组件命名规范
```
格式: [Category]/[Component]/[Variant]
示例: 
- Button/Primary/Default
- Input/Text/Focused
- Card/Goose/Default
```

### 组件状态管理
- 使用Figma变体管理不同状态
- 保持状态命名一致性
- 设置默认状态

### Auto Layout设置
- 主轴: 根据内容方向设置
- 交叉轴: 通常居中对齐
- 间距: 使用设计Token
- 填充: 设置合理的内边距

### 约束设置
- 水平: Left & Right (拉伸)
- 垂直: Top (固定顶部)
- 特殊情况: Center (居中)

## 🔧 开发交接规范

### 导出设置
- 图标: SVG格式, 1x/2x/3x
- 图片: PNG格式, 2x/3x
- 颜色: HEX/RGB值
- 字体: 具体数值和权重

### 标注信息
- 尺寸: 宽度、高度、间距
- 颜色: 精确的颜色值
- 字体: 字号、行高、字重
- 效果: 阴影、圆角、透明度

### 交互说明
- 状态变化: 详细描述
- 动画效果: 时长和缓动
- 触发条件: 用户操作
- 反馈机制: 视觉反馈

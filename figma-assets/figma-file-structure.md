# 小鹅养成APP - Figma文件结构模板

## 📁 文件组织结构

### 主文件：小鹅养成APP - 设计系统
```
📄 小鹅养成APP - 设计系统.fig
├── 📑 Cover (封面页)
├── 📑 Design System (设计系统)
├── 📑 Components (组件库)
├── 📑 Pages - Core (核心页面)
├── 📑 Pages - Secondary (次要页面)
├── 📑 Prototypes (原型流程)
└── 📑 Archive (归档)
```

## 📑 页面详细结构

### 1. Cover (封面页)
```
内容：
- 项目标题: "小鹅养成APP设计系统"
- 版本信息: "v1.0"
- 更新日期: "2025-06-13"
- 设计师信息
- 项目概述
- 快速导航链接

尺寸: 1200 x 800px
背景: 品牌渐变色
```

### 2. Design System (设计系统)
```
2.1 Colors (颜色系统)
├── Primary Colors (主色调)
│   ├── Primary Yellow (#FFD700)
│   ├── Primary Orange (#FFA500)
│   └── Primary Pink (#FFB6C1)
├── Neutral Colors (中性色)
│   ├── Text Colors (文字颜色)
│   └── Background Colors (背景颜色)
├── Status Colors (状态色)
│   ├── Success (#4CAF50)
│   ├── Warning (#FF9800)
│   ├── Error (#F44336)
│   └── Info (#2196F3)
└── Glass Effect (玻璃效果)
    ├── Glass Background
    ├── Glass Border
    └── Glass Shadow

2.2 Typography (字体系统)
├── Font Sizes (字号)
├── Font Weights (字重)
├── Line Heights (行高)
└── Text Styles (文字样式)
    ├── Display
    ├── Title
    ├── Subtitle
    ├── Body Large
    ├── Body Medium
    ├── Body Small
    └── Caption

2.3 Spacing (间距系统)
├── Base Unit: 8px
├── Spacing Scale
│   ├── XS: 4px
│   ├── S: 8px
│   ├── M: 16px
│   ├── L: 24px
│   ├── XL: 32px
│   └── XXL: 48px
└── Layout Grid (布局网格)

2.4 Effects (效果系统)
├── Shadows (阴影)
├── Blur (模糊)
├── Border Radius (圆角)
└── Opacity (透明度)

2.5 Icons (图标系统)
├── Navigation Icons (导航图标)
├── Action Icons (操作图标)
├── Status Icons (状态图标)
└── Emoji Icons (表情图标)
```

### 3. Components (组件库)
```
3.1 Atoms (原子组件)
├── Buttons
│   ├── Primary Button
│   ├── Secondary Button
│   ├── Text Button
│   └── Icon Button
├── Inputs
│   ├── Text Input
│   ├── Search Input
│   └── Number Input
├── Icons
├── Avatars
└── Badges

3.2 Molecules (分子组件)
├── Form Fields
├── Search Bar
├── Navigation Items
├── Status Indicators
└── Progress Bars

3.3 Organisms (有机体组件)
├── Cards
│   ├── Goose Card
│   ├── Product Card
│   ├── Post Card
│   └── Info Card
├── Navigation
│   ├── Bottom Navigation
│   ├── Top Navigation
│   └── Tab Bar
├── Lists
│   ├── Goose List
│   ├── Product List
│   └── Post List
└── Modals
    ├── Dialog
    ├── Bottom Sheet
    └── Alert

3.4 Templates (模板组件)
├── Page Layout
├── Modal Layout
└── List Layout
```

### 4. Pages - Core (核心页面)
```
4.1 Authentication (认证流程)
├── 01-Login (登录页)
│   ├── Desktop: 375 x 812px
│   ├── States: Default, Loading, Error
│   └── Variants: Phone, WeChat

4.2 Main Flow (主要流程)
├── 02-My Geese (我的鹅页)
│   ├── States: Empty, Loaded, Loading
│   └── Variants: 1 Goose, Multiple Geese
├── 03-Goose Detail (小鹅详情页)
│   ├── States: Healthy, Hungry, Sick
│   └── Actions: Feed, Clean, Play
├── 04-Shop (商城页)
│   ├── Categories: All, White, Gray, Limited
│   └── States: Loading, Loaded, Empty

4.3 Discovery (发现功能)
├── 05-Discover (发现页)
│   ├── Sections: Activities, Recommendations, Feed
│   └── States: Loading, Loaded
└── 06-Social Feed (动态页)
    ├── Post Types: Text, Image, Video
    └── Actions: Like, Comment, Share
```

### 5. Pages - Secondary (次要页面)
```
5.1 User Management (用户管理)
├── 07-Profile (个人中心)
│   ├── User Info
│   ├── Statistics
│   └── Menu Items
├── 08-Settings (设置页)
│   ├── Account Settings
│   ├── Notification Settings
│   └── General Settings

5.2 Special Flows (特殊流程)
├── 09-Gifting Flow (转赠流程)
│   ├── Select Goose
│   ├── Add Message
│   └── Share Options
└── 10-Purchase Flow (购买流程)
    ├── Product Selection
    ├── Order Summary
    └── Payment
```

### 6. Prototypes (原型流程)
```
6.1 Main User Flow (主要用户流程)
├── Login → My Geese → Goose Detail
├── Shop → Product → Purchase
└── Discover → Social Feed

6.2 Secondary Flows (次要流程)
├── Profile → Settings
├── Goose Detail → Gifting
└── Error Handling

6.3 Micro Interactions (微交互)
├── Button States
├── Loading States
├── Transition Animations
└── Feedback Effects
```

### 7. Archive (归档)
```
7.1 Old Versions (旧版本)
├── v0.1 - Initial Concepts
├── v0.2 - First Iteration
└── v0.3 - Refined Design

7.2 Explorations (探索)
├── Alternative Layouts
├── Color Experiments
└── Typography Tests

7.3 Assets (资源)
├── Original Screenshots
├── Reference Images
└── Inspiration
```

## 🏷️ 命名规范

### Frame命名
```
格式: [序号]-[页面名称]-[状态]
示例:
- 01-Login-Default
- 02-MyGeese-Loaded
- 03-GooseDetail-Healthy
```

### 组件命名
```
格式: [类型]/[组件名]/[变体]
示例:
- Button/Primary/Default
- Card/Goose/Healthy
- Input/Search/Focused
```

### 图层命名
```
格式: [功能描述]
示例:
- Header Navigation
- Goose Avatar
- Action Buttons
- Status Indicators
```

## 🎨 样式管理

### 颜色样式命名
```
格式: [用途]/[颜色名]
示例:
- Primary/Yellow
- Text/Primary
- Background/Surface
- Status/Success
```

### 文字样式命名
```
格式: [层级]/[用途]
示例:
- Display/Page Title
- Body/Primary Text
- Caption/Helper Text
```

### 效果样式命名
```
格式: [类型]/[强度]
示例:
- Shadow/Card
- Shadow/Button
- Blur/Glass
```

## 📋 组件管理

### 组件状态
- Default (默认)
- Hover (悬停)
- Pressed (按下)
- Focused (聚焦)
- Disabled (禁用)
- Loading (加载)
- Error (错误)

### 组件变体
- Size: Small, Medium, Large
- Type: Primary, Secondary, Tertiary
- State: 如上所述
- Content: 根据内容变化

### Auto Layout设置
- Direction: Horizontal/Vertical
- Alignment: Start, Center, End
- Spacing: 使用设计Token
- Padding: 统一内边距

## 🔗 原型连接

### 交互类型
- On Click (点击)
- On Hover (悬停)
- On Drag (拖拽)
- After Delay (延迟)

### 动画设置
- Transition: Smart Animate
- Duration: 300ms (标准)
- Easing: Ease Out
- Direction: 根据逻辑设置

### 覆盖层
- Modal: Center
- Bottom Sheet: Bottom
- Toast: Top
- Tooltip: Smart Position

## 📱 设备适配

### 画布尺寸
- iPhone SE: 375 x 667px
- iPhone 12: 375 x 812px
- iPhone 12 Pro Max: 414 x 896px
- Android Standard: 360 x 640px

### 约束设置
- 水平: Left & Right (拉伸)
- 垂直: Top (固定顶部)
- 特殊: Center (居中对齐)

### 响应式规则
- 最小宽度: 320px
- 最大宽度: 480px
- 内容区域: 16px边距
- 安全区域: 考虑刘海和底部指示器

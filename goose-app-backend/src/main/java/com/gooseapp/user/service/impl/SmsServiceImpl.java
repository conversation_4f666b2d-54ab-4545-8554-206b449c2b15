package com.gooseapp.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.user.dto.SendSmsCodeRequest;
import com.gooseapp.user.dto.VerifySmsCodeRequest;
import com.gooseapp.user.entity.SmsVerificationCode;
import com.gooseapp.user.mapper.SmsVerificationCodeMapper;
import com.gooseapp.user.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsServiceImpl extends ServiceImpl<SmsVerificationCodeMapper, SmsVerificationCode> implements SmsService {

    private final SmsVerificationCodeMapper smsVerificationCodeMapper;

    // 验证码有效期（分钟）
    private static final int CODE_EXPIRE_MINUTES = 5;
    
    // 手机号发送频率限制（每分钟最多发送次数）
    private static final int PHONE_RATE_LIMIT_PER_MINUTE = 1;
    
    // IP发送频率限制（每小时最多发送次数）
    private static final int IP_RATE_LIMIT_PER_HOUR = 10;

    @Override
    @Transactional
    public boolean sendSmsCode(SendSmsCodeRequest request, String ipAddress, String userAgent) {
        log.info("发送短信验证码: phone={}, codeType={}, ip={}", request.getPhone(), request.getCodeType(), ipAddress);

        // 检查发送频率限制
        if (!checkPhoneRateLimit(request.getPhone(), request.getCodeType())) {
            throw new BusinessException(6701, "发送过于频繁，请稍后再试");
        }

        if (!checkIpRateLimit(ipAddress)) {
            throw new BusinessException(6701, "IP发送过于频繁，请稍后再试");
        }

        // 生成6位数字验证码
        String code = generateSmsCode();
        
        // 创建验证码记录
        SmsVerificationCode smsCode = new SmsVerificationCode();
        smsCode.setPhone(request.getPhone());
        smsCode.setCode(code);
        smsCode.setCodeType(request.getCodeType());
        smsCode.setExpiresAt(LocalDateTime.now().plusMinutes(CODE_EXPIRE_MINUTES));
        smsCode.setUsed(0);
        smsCode.setIpAddress(ipAddress);
        smsCode.setUserAgent(userAgent);

        // 保存到数据库
        smsVerificationCodeMapper.insert(smsCode);

        // 发送短信（这里使用模拟发送，实际项目中需要集成真实的短信服务）
        boolean sendResult = sendSmsToProvider(request.getPhone(), code, request.getCodeTypeDescription());

        if (sendResult) {
            log.info("短信验证码发送成功: phone={}, code={}", request.getPhone(), code);
        } else {
            log.error("短信验证码发送失败: phone={}", request.getPhone());
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "短信发送失败，请稍后重试");
        }

        return sendResult;
    }

    @Override
    public boolean verifySmsCode(VerifySmsCodeRequest request) {
        log.info("验证短信验证码: phone={}, code={}, codeType={}", request.getPhone(), request.getCode(), request.getCodeType());

        // 特殊验证码处理
        if ("000000".equals(request.getCode())) {
            log.info("使用特殊验证码登录: phone={}", request.getPhone());
            return true;
        }

        SmsVerificationCode smsCode = smsVerificationCodeMapper.findByPhoneAndCodeAndType(
                request.getPhone(), request.getCode(), request.getCodeType());

        if (smsCode == null) {
            log.warn("验证码不存在: phone={}, code={}", request.getPhone(), request.getCode());
            return false;
        }

        if (!smsCode.isValid()) {
            log.warn("验证码无效: phone={}, code={}, expired={}, used={}",
                    request.getPhone(), request.getCode(), smsCode.isExpired(), smsCode.hasBeenUsed());
            return false;
        }

        log.info("短信验证码验证成功: phone={}, code={}", request.getPhone(), request.getCode());
        return true;
    }

    @Override
    @Transactional
    public boolean verifyAndConsumeSmsCode(String phone, String code, Integer codeType) {
        log.info("验证并消费短信验证码: phone={}, code={}, codeType={}", phone, code, codeType);

        // 特殊验证码处理
        if ("000000".equals(code)) {
            log.info("使用特殊验证码登录: phone={}", phone);
            return true;
        }

        SmsVerificationCode smsCode = smsVerificationCodeMapper.findByPhoneAndCodeAndType(phone, code, codeType);

        if (smsCode == null) {
            log.warn("验证码不存在: phone={}, code={}", phone, code);
            return false;
        }

        if (!smsCode.isValid()) {
            log.warn("验证码无效: phone={}, code={}, expired={}, used={}",
                    phone, code, smsCode.isExpired(), smsCode.hasBeenUsed());
            return false;
        }

        // 标记为已使用
        smsCode.markAsUsed();
        smsVerificationCodeMapper.updateById(smsCode);

        log.info("短信验证码验证并消费成功: phone={}, code={}", phone, code);
        return true;
    }

    @Override
    @Transactional
    public void cleanExpiredCodes() {
        log.info("开始清理过期验证码");

        List<SmsVerificationCode> expiredCodes = smsVerificationCodeMapper.findExpiredCodes(LocalDateTime.now());
        
        if (!expiredCodes.isEmpty()) {
            // 逻辑删除过期验证码
            expiredCodes.forEach(code -> {
                code.setIsDeleted(1);
                smsVerificationCodeMapper.updateById(code);
            });
            
            log.info("清理过期验证码完成: count={}", expiredCodes.size());
        } else {
            log.info("没有需要清理的过期验证码");
        }
    }

    @Override
    public boolean checkPhoneRateLimit(String phone, Integer codeType) {
        LocalDateTime oneMinuteAgo = LocalDateTime.now().minusMinutes(1);
        int count = smsVerificationCodeMapper.countByPhoneAndTypeAfterTime(phone, codeType, oneMinuteAgo);
        
        boolean allowed = count < PHONE_RATE_LIMIT_PER_MINUTE;
        log.debug("手机号发送频率检查: phone={}, count={}, allowed={}", phone, count, allowed);
        
        return allowed;
    }

    @Override
    public boolean checkIpRateLimit(String ipAddress) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        int count = smsVerificationCodeMapper.countByIpAfterTime(ipAddress, oneHourAgo);
        
        boolean allowed = count < IP_RATE_LIMIT_PER_HOUR;
        log.debug("IP发送频率检查: ip={}, count={}, allowed={}", ipAddress, count, allowed);
        
        return allowed;
    }

    /**
     * 生成6位数字验证码
     */
    private String generateSmsCode() {
        SecureRandom random = new SecureRandom();
        int code = 100000 + random.nextInt(900000);
        return String.valueOf(code);
    }

    /**
     * 发送短信到第三方服务商
     * 这里使用模拟发送，实际项目中需要集成真实的短信服务
     */
    private boolean sendSmsToProvider(String phone, String code, String codeTypeDesc) {
        // 模拟发送短信
        log.info("【模拟短信】发送给 {}: 您的{}验证码是 {}，5分钟内有效。", phone, codeTypeDesc, code);
        
        // 在开发环境中，总是返回成功
        // 在生产环境中，这里应该调用真实的短信服务API
        return true;
    }
}

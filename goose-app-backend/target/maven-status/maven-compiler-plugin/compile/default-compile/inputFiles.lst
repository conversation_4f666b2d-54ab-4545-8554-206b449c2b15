/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/UserRegisterDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/UserProfileUpdateDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/UserService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/mapper/UserLoginLogMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/GooseAppApplication.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/dto/GiftStatisticsDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/TokenRefreshResponse.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/UserLoginDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/dto/GooseDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/dto/PurchaseResultDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/service/PurchaseService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/interceptor/PermissionInterceptor.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/entity/UserRole.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/SendSmsCodeRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/admin/service/AdminGooseService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/impl/UserStatsServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/impl/SmsServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/response/ApiResponse.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/service/InteractionService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/SmsService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/service/impl/InteractionServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/entity/Gift.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/MobileAccountResponse.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/enums/Role.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/UserLoginLogService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/VerifySmsCodeRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/entity/Interaction.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/SetMobilePasswordRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/UserRoleDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/mapper/UserMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/mapper/GiftMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/UserStatsService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/exception/BusinessException.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/event/GiftEventListener.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/UserProfileDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/entity/SmsVerificationCode.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/service/impl/GiftServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/mapper/SmsVerificationCodeMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/dto/GiftReceiveRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/dto/GoosePurchaseDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/service/impl/PurchaseServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/impl/UserLoginLogServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/utils/PasswordUtils.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/service/GooseService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/mapper/GoosePurchaseMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/admin/controller/AdminGooseController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/mapper/GooseMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/admin/dto/AdminGooseCreateRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/admin/dto/BatchCreateGooseRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/admin/service/impl/AdminGooseServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/config/WebConfig.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/mapper/UserRoleMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/exception/GlobalExceptionHandler.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/entity/GoosePurchase.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/CreateMobileAccountRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/PhoneLoginRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/controller/AuthController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/dto/GooseDetailDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/controller/UserController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/controller/InteractionController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/dto/InteractionDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/annotation/AdminRequired.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/config/GiftConfig.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/dto/GiftCreateRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/service/GiftService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/controller/GooseController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/task/GiftScheduledTask.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/entity/UserLoginLog.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/controller/ShopController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/entity/BaseEntity.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/entity/User.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/RoleService.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/impl/RoleServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/dto/GiftDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/controller/GiftController.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/LoginResponse.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/dto/PhoneRegisterRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/entity/Goose.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/shop/dto/PurchaseGooseRequest.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/event/GiftEvent.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/config/MyBatisPlusConfig.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/user/service/impl/UserServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/constant/ErrorCode.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/gift/dto/GiftListDTO.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/mapper/InteractionMapper.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/goose/service/impl/GooseServiceImpl.java
/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment/goose-app-backend/src/main/java/com/gooseapp/common/config/SwaggerConfig.java

-- 小鹅养成APP数据库初始化脚本
-- 创建时间: 2024-12-19
-- 版本: 1.0.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS goose_app
DEFAULT CHARACTER SET utf8mb4
DEFAULT COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE goose_app;

-- 创建用户
CREATE USER IF NOT EXISTS 'goose_user'@'localhost' IDENTIFIED BY 'goose_password';
GRANT ALL PRIVILEGES ON goose_app.* TO 'goose_user'@'localhost';
FLUSH PRIVILEGES;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    birthday DATE COMMENT '生日',
    total_geese INT DEFAULT 0 COMMENT '拥有小鹅总数',
    total_days INT DEFAULT 0 COMMENT '养成总天数',
    total_feeds INT DEFAULT 0 COMMENT '喂食总次数',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 小鹅表 (移除外键约束，采用逻辑关联)
CREATE TABLE IF NOT EXISTS geese (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '小鹅ID',
    user_id BIGINT NOT NULL COMMENT '所属用户ID',
    name VARCHAR(50) NOT NULL COMMENT '小鹅名称',
    breed VARCHAR(30) NOT NULL COMMENT '品种',
    gender TINYINT NOT NULL COMMENT '性别：1-雄性，2-雌性',
    age INT DEFAULT 0 COMMENT '年龄（周）',
    health INT DEFAULT 100 COMMENT '健康度（0-100）',
    hunger INT DEFAULT 50 COMMENT '饥饿度（0-100）',
    thirst INT DEFAULT 50 COMMENT '口渴度（0-100）',
    cleanliness INT DEFAULT 100 COMMENT '清洁度（0-100）',
    happiness INT DEFAULT 80 COMMENT '快乐度（0-100）',
    level INT DEFAULT 1 COMMENT '等级',
    experience INT DEFAULT 0 COMMENT '经验值',
    image_url VARCHAR(255) COMMENT '小鹅图片URL',
    video_url VARCHAR(255) COMMENT '直播视频URL',
    last_feed_time TIMESTAMP NULL COMMENT '最后喂食时间',
    last_water_time TIMESTAMP NULL COMMENT '最后喂水时间',
    last_clean_time TIMESTAMP NULL COMMENT '最后清洁时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已转赠，1-正常，2-生病',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 移除外键约束，通过业务逻辑保证数据一致性
    INDEX idx_user_id (user_id),
    INDEX idx_breed (breed),
    INDEX idx_status (status),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小鹅表';

-- 商品表
CREATE TABLE IF NOT EXISTS products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    breed VARCHAR(30) NOT NULL COMMENT '小鹅品种',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    category VARCHAR(30) NOT NULL COMMENT '分类',
    age_range VARCHAR(20) COMMENT '年龄范围',
    health_range VARCHAR(20) COMMENT '健康度范围',
    image_url VARCHAR(255) COMMENT '商品图片URL',
    images JSON COMMENT '商品图片列表',
    tags JSON COMMENT '标签列表',
    stock INT DEFAULT 0 COMMENT '库存数量',
    sales_count INT DEFAULT 0 COMMENT '销售数量',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分（1-5）',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_limited BOOLEAN DEFAULT FALSE COMMENT '是否限量',
    status TINYINT DEFAULT 1 COMMENT '状态：0-下架，1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_category (category),
    INDEX idx_breed (breed),
    INDEX idx_price (price),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_sales_count (sales_count),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 订单表 (移除外键约束，采用逻辑关联)
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    payment_method VARCHAR(20) COMMENT '支付方式',
    payment_status TINYINT DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-已退款',
    order_status TINYINT DEFAULT 0 COMMENT '订单状态：0-待支付，1-已支付，2-已完成，3-已取消',
    remark TEXT COMMENT '订单备注',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 移除外键约束，通过业务逻辑保证数据一致性
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_order_status (order_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单项表 (移除外键约束，采用逻辑关联)
CREATE TABLE IF NOT EXISTS order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单项ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(255) COMMENT '商品图片',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    quantity INT NOT NULL DEFAULT 1 COMMENT '购买数量',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 移除外键约束，通过业务逻辑保证数据一致性
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单项表';

-- 互动记录表 (移除外键约束，采用逻辑关联)
CREATE TABLE IF NOT EXISTS interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '互动记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    interaction_type VARCHAR(20) NOT NULL COMMENT '互动类型：feed-喂食，water-喂水，clean-清洁，play-玩耍',
    value_change INT DEFAULT 0 COMMENT '数值变化',
    experience_gained INT DEFAULT 0 COMMENT '获得经验',
    cooldown_minutes INT DEFAULT 0 COMMENT '冷却时间（分钟）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 移除外键约束，通过业务逻辑保证数据一致性
    INDEX idx_user_id (user_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='互动记录表';

-- 转赠记录表 (移除外键约束，采用逻辑关联)
CREATE TABLE IF NOT EXISTS gifts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转赠记录ID',
    gift_code VARCHAR(32) NOT NULL UNIQUE COMMENT '转赠码',
    sender_id BIGINT NOT NULL COMMENT '赠送者ID',
    receiver_id BIGINT NULL COMMENT '接收者ID',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    message TEXT COMMENT '祝福语',
    share_method VARCHAR(20) NOT NULL COMMENT '分享方式：wechat-微信，link-链接',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待领取，1-已领取，2-已过期',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    received_at TIMESTAMP NULL COMMENT '领取时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 移除外键约束，通过业务逻辑保证数据一致性
    INDEX idx_gift_code (gift_code),
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转赠记录表';

-- ========================================
-- 插入测试数据
-- ========================================

-- 插入用户测试数据 (密码都是: password123，除了123用户的密码是123)
INSERT INTO users (username, email, password, nickname, avatar, phone, gender, birthday, total_geese, total_days, total_feeds, status) VALUES
('123', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户123', '🦆', '123', 1, '2000-01-01', 1, 1, 1, 1),
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGjO6NjMYLvpQr4QJdj2Ci', '测试用户', '👤', '13800138001', 1, '1995-06-15', 2, 15, 42, 1),
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGjO6NjMYLvpQr4QJdj2Ci', '管理员', '👨‍💼', '13800138002', 1, '1990-01-01', 0, 0, 0, 1),
('alice', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGjO6NjMYLvpQr4QJdj2Ci', '爱丽丝', '👩', '13800138003', 2, '1998-03-20', 1, 8, 25, 1),
('bob', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGjO6NjMYLvpQr4QJdj2Ci', '鲍勃', '👨', '13800138004', 1, '1992-11-08', 3, 22, 68, 1),
('charlie', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGjO6NjMYLvpQr4QJdj2Ci', '查理', '🧑', '13800138005', 0, '2000-07-12', 1, 5, 18, 1);

-- 插入商品测试数据
INSERT INTO products (name, description, breed, price, original_price, category, age_range, health_range, image_url, tags, stock, sales_count, rating, is_featured, is_limited, status) VALUES
('小白鹅·豆豆', '温顺可爱的小白鹅，适合新手养成。性格温和，容易照顾，是初次养鹅用户的最佳选择。', '白鹅', 199.00, 299.00, '新手推荐', '1-2周', '85-95%', '/images/white_goose_1.jpg', '["新手友好", "温顺", "可爱"]', 50, 128, 4.8, TRUE, FALSE, 1),
('小灰鹅·灰灰', '聪明活泼的小灰鹅，互动性强。喜欢与主人互动，学习能力强，适合有经验的用户。', '灰鹅', 299.00, 399.00, '热门推荐', '2-3周', '88-98%', '/images/gray_goose_1.jpg', '["聪明", "活泼", "互动"]', 30, 95, 4.7, TRUE, FALSE, 1),
('小花鹅·花花', '稀有品种小花鹅，颜值超高。羽毛花纹独特，极具观赏价值，限量发售。', '花鹅', 399.00, 499.00, '限量版', '3-4周', '90-100%', '/images/flower_goose_1.jpg', '["稀有", "高颜值", "限量"]', 10, 45, 4.9, TRUE, TRUE, 1),
('小黑鹅·小黑', '神秘优雅的小黑鹅，成长潜力大。外表酷炫，成长速度快，适合追求个性的用户。', '黑鹅', 159.00, 199.00, '潜力股', '1周', '80-90%', '/images/black_goose_1.jpg', '["神秘", "优雅", "潜力"]', 40, 72, 4.6, FALSE, FALSE, 1),
('小金鹅·金金', '珍贵的金色小鹅，象征财富和好运。羽毛金光闪闪，是收藏家的最爱。', '金鹅', 599.00, 799.00, '珍藏版', '4-5周', '95-100%', '/images/golden_goose_1.jpg', '["珍贵", "金色", "收藏"]', 5, 23, 5.0, TRUE, TRUE, 1),
('小棕鹅·棕棕', '朴实可靠的棕色小鹅，生命力顽强。适应能力强，容易养活，性价比很高。', '棕鹅', 129.00, 159.00, '经济实惠', '1周', '75-85%', '/images/brown_goose_1.jpg', '["朴实", "可靠", "经济"]', 60, 156, 4.5, FALSE, FALSE, 1);

-- 插入小鹅测试数据
INSERT INTO geese (user_id, name, breed, gender, age, health, hunger, thirst, cleanliness, happiness, level, experience, image_url, video_url, last_feed_time, last_water_time, last_clean_time, status) VALUES
(1, '小白', '白鹅', 2, 3, 85, 60, 45, 75, 80, 2, 150, '/images/white_goose_1.jpg', '/videos/live_stream_1.m3u8', '2024-12-19 08:30:00', '2024-12-19 09:15:00', '2024-12-19 07:45:00', 1),
(1, '小灰', '灰鹅', 1, 5, 92, 40, 55, 88, 85, 3, 280, '/images/gray_goose_1.jpg', '/videos/live_stream_2.m3u8', '2024-12-19 07:20:00', '2024-12-19 08:45:00', '2024-12-19 06:30:00', 1),
(3, '花花', '花鹅', 2, 4, 95, 35, 60, 90, 88, 3, 320, '/images/flower_goose_1.jpg', '/videos/live_stream_3.m3u8', '2024-12-19 09:10:00', '2024-12-19 09:30:00', '2024-12-19 08:15:00', 1),
(4, '小黑', '黑鹅', 1, 2, 78, 70, 50, 65, 75, 1, 80, '/images/black_goose_1.jpg', '/videos/live_stream_4.m3u8', '2024-12-19 06:45:00', '2024-12-19 07:30:00', '2024-12-19 05:20:00', 1),
(4, '金金', '金鹅', 2, 6, 98, 25, 40, 95, 92, 4, 450, '/images/golden_goose_1.jpg', '/videos/live_stream_5.m3u8', '2024-12-19 08:00:00', '2024-12-19 08:20:00', '2024-12-19 07:10:00', 1),
(4, '棕棕', '棕鹅', 1, 1, 82, 55, 65, 70, 78, 1, 45, '/images/brown_goose_1.jpg', '/videos/live_stream_6.m3u8', '2024-12-19 09:00:00', '2024-12-19 09:20:00', '2024-12-19 08:30:00', 1),
(5, '雪球', '白鹅', 2, 2, 88, 45, 50, 80, 82, 2, 120, '/images/white_goose_2.jpg', '/videos/live_stream_7.m3u8', '2024-12-19 07:50:00', '2024-12-19 08:10:00', '2024-12-19 06:45:00', 1);

-- 插入互动记录测试数据
INSERT INTO interactions (user_id, goose_id, interaction_type, value_change, experience_gained, cooldown_minutes) VALUES
(1, 1, 'feed', 15, 5, 60),
(1, 1, 'water', 10, 3, 30),
(1, 1, 'clean', 20, 8, 120),
(1, 2, 'feed', 12, 4, 60),
(1, 2, 'play', 8, 6, 45),
(3, 3, 'feed', 18, 6, 60),
(3, 3, 'water', 12, 4, 30),
(4, 4, 'feed', 10, 3, 60),
(4, 5, 'feed', 20, 8, 60),
(4, 5, 'clean', 25, 10, 120),
(4, 6, 'water', 8, 2, 30),
(5, 7, 'feed', 14, 5, 60),
(5, 7, 'play', 6, 4, 45);

-- 插入订单测试数据
INSERT INTO orders (order_no, user_id, total_amount, discount_amount, final_amount, payment_method, payment_status, order_status, remark, paid_at, completed_at) VALUES
('GO20241201001', 1, 498.00, 0.00, 498.00, 'wechat', 1, 2, '请尽快发货', '2024-12-01 10:30:00', '2024-12-01 10:35:00'),
('GO20241205002', 3, 399.00, 50.00, 349.00, 'alipay', 1, 2, '生日礼物', '2024-12-05 14:20:00', '2024-12-05 14:25:00'),
('GO20241210003', 4, 757.00, 0.00, 757.00, 'wechat', 1, 2, '批量购买', '2024-12-10 16:45:00', '2024-12-10 16:50:00'),
('GO20241215004', 5, 199.00, 20.00, 179.00, 'alipay', 1, 2, '新手入门', '2024-12-15 09:15:00', '2024-12-15 09:20:00'),
('GO20241218005', 1, 299.00, 0.00, 299.00, 'wechat', 0, 0, '待支付订单', NULL, NULL);

-- 插入订单项测试数据
INSERT INTO order_items (order_id, product_id, product_name, product_image, price, quantity, total_price) VALUES
(1, 1, '小白鹅·豆豆', '/images/white_goose_1.jpg', 199.00, 1, 199.00),
(1, 2, '小灰鹅·灰灰', '/images/gray_goose_1.jpg', 299.00, 1, 299.00),
(2, 3, '小花鹅·花花', '/images/flower_goose_1.jpg', 399.00, 1, 399.00),
(3, 4, '小黑鹅·小黑', '/images/black_goose_1.jpg', 159.00, 1, 159.00),
(3, 5, '小金鹅·金金', '/images/golden_goose_1.jpg', 599.00, 1, 599.00),
(4, 1, '小白鹅·豆豆', '/images/white_goose_1.jpg', 199.00, 1, 199.00),
(5, 2, '小灰鹅·灰灰', '/images/gray_goose_1.jpg', 299.00, 1, 299.00);

-- 插入转赠记录测试数据
INSERT INTO gifts (gift_code, sender_id, receiver_id, goose_id, message, share_method, status, expires_at, received_at) VALUES
('GIFT20241201001', 1, 3, 1, '希望这只小鹅能给你带来快乐！生日快乐！', 'wechat', 1, '2024-12-04 10:30:00', '2024-12-02 15:20:00'),
('GIFT20241210002', 4, NULL, 4, '分享一只可爱的小黑鹅，先到先得哦~', 'link', 0, '2024-12-22 16:45:00', NULL),
('GIFT20241215003', 1, 5, 2, '新手礼物，希望你喜欢养鹅的乐趣！', 'wechat', 1, '2024-12-18 09:15:00', '2024-12-16 11:30:00');

COMMIT;

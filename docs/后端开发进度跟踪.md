# 后端开发进度跟踪

## 2025-06-14

### **特性：手机号登录功能调试与修复**

-   **状态：** `已完成`
-   **主要贡献者：** 高级全栈开发工程师
-   **来源任务清单：** docs/任务清单/2025-06-14/01-手机号登录-任务清单.md
-   **变更描述：**
    -   解决了手机号登录功能中的验证规则问题，支持测试模式下的简化手机号格式。
    -   修改了6个相关DTO类的手机号验证正则表达式，从严格的11位手机号验证改为支持测试场景。
    -   在数据库中创建了手机号为"123"的测试用户，完善了测试数据。
    -   验证了万能验证码"000000"的正常工作。
-   **技术实现：**
    -   **验证规则优化**: 将正则表达式从 `^1[3-9]\\d{9}$` 修改为 `^(1[3-9]\\d{9}|\\d{3,})$`
    -   **涉及文件**: PhoneLoginRequest、SendSmsCodeRequest、VerifySmsCodeRequest、PhoneRegisterRequest、CreateMobileAccountRequest、UserRegisterDTO
    -   **测试用户**: 创建了ID为29的测试用户(test123/123)，支持手机号登录测试
-   **质量保证：**
    -   修改完全向后兼容，不影响正式用户的手机号验证
    -   统一了所有相关DTO的验证规则，保持代码一致性
    -   重启服务验证功能正常工作

## 2025-06-12

### **特性：核心用户服务 (`UserService`) 重构**

-   **状态：** `已完成`
-   **主要贡献者：** 高级全栈工程师
-   **变更描述：**
    -   对原有的上帝服务 `UserServiceImpl` 进行了彻底的重构，遵循单一职责原则。
    -   成功将用户统计相关逻辑 (`getUserStats`, `updateUserStats`) 剥离，迁移至独立的 `UserStatsServiceImpl`。
    -   成功将用户登录日志记录逻辑 (`recordLoginLog`) 剥离，迁移至独立的 `UserLoginLogServiceImpl`。
    -   为新拆分出的两个服务编写了全面的单元测试，确保其逻辑的正确性和稳定性。
    -   通过端到端的集成测试，验证了重构后的注册与登录流程，确保核心功能未受影响并按预期工作。
-   **解决的关键问题：**
    -   在集成测试中，定位并修复了一系列因历史技术债导致的严重问题，包括：
        1.  **MyBatis绑定异常 (`BindingException`)**: 修正了自定义Repository与MyBatis-Plus在方法命名上的冲突（如 `findById` vs `selectById`）。
        2.  **组件依赖混乱**: 纠正了服务层错误依赖自定义的、权责不清的`UserRepository`，改为直接依赖功能明确的`UserMapper`。
    -   本次重构不仅优化了代码结构，更清理了潜藏的、可能导致未来更多bug的架构缺陷。